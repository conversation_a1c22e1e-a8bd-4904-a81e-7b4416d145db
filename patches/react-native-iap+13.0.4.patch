diff --git a/node_modules/react-native-iap/ios/RNIapIosSk2.swift b/node_modules/react-native-iap/ios/RNIapIosSk2.swift
index c389f6d..c97664a 100644
--- a/node_modules/react-native-iap/ios/RNIapIosSk2.swift
+++ b/node_modules/react-native-iap/ios/RNIapIosSk2.swift
@@ -1198,12 +1198,6 @@ class RNIapIosSk2iOS15: Sk2Delegate {
                     case .verified(let appTransaction):
                         var result: [String: Any] = [:]
 
-                        // Add iOS 18.4+ properties
-                        if #available(iOS 18.4, tvOS 18.4, *) {
-                            result["appTransactionID"] = appTransaction.appTransactionID
-                            result["originalPlatform"] = appTransaction.originalPlatform
-                        }
-
                         // Add other AppTransaction properties
                         result["originalAppVersion"] = appTransaction.originalAppVersion
                         result["originalPurchaseDate"] = appTransaction.originalPurchaseDate.timeIntervalSince1970 * 1000
