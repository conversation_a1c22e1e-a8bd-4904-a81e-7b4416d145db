diff --git a/node_modules/react-native-image-crop-picker/android/build/.transforms/26816c35a2b533124315f466bb8eb6f0/results.bin b/node_modules/react-native-image-crop-picker/android/build/.transforms/26816c35a2b533124315f466bb8eb6f0/results.bin
new file mode 100644
index 0000000..e3f0ff0
--- /dev/null
+++ b/node_modules/react-native-image-crop-picker/android/build/.transforms/26816c35a2b533124315f466bb8eb6f0/results.bin
@@ -0,0 +1 @@
+i/classes_global-synthetics
diff --git a/node_modules/react-native-image-crop-picker/android/build/.transforms/a8c706b04cd8bdf15bcb980d0ca25a49/results.bin b/node_modules/react-native-image-crop-picker/android/build/.transforms/a8c706b04cd8bdf15bcb980d0ca25a49/results.bin
new file mode 100644
index 0000000..61a58fa
--- /dev/null
+++ b/node_modules/react-native-image-crop-picker/android/build/.transforms/a8c706b04cd8bdf15bcb980d0ca25a49/results.bin
@@ -0,0 +1 @@
+i/classes_dex
diff --git a/node_modules/react-native-image-crop-picker/android/build/.transforms/cd7f2f4d768c1d3611ea20e5a620dd72/results.bin b/node_modules/react-native-image-crop-picker/android/build/.transforms/cd7f2f4d768c1d3611ea20e5a620dd72/results.bin
new file mode 100644
index 0000000..0d259dd
--- /dev/null
+++ b/node_modules/react-native-image-crop-picker/android/build/.transforms/cd7f2f4d768c1d3611ea20e5a620dd72/results.bin
@@ -0,0 +1 @@
+o/classes
diff --git a/node_modules/react-native-image-crop-picker/android/build/.transforms/cd7f2f4d768c1d3611ea20e5a620dd72/transformed/classes/classes_dex/classes.dex b/node_modules/react-native-image-crop-picker/android/build/.transforms/cd7f2f4d768c1d3611ea20e5a620dd72/transformed/classes/classes_dex/classes.dex
new file mode 100644
index 0000000..358c7b1
Binary files /dev/null and b/node_modules/react-native-image-crop-picker/android/build/.transforms/cd7f2f4d768c1d3611ea20e5a620dd72/transformed/classes/classes_dex/classes.dex differ
diff --git a/node_modules/react-native-image-crop-picker/android/build/.transforms/e23c948fa969bd4663f8552b27cbfb55/results.bin b/node_modules/react-native-image-crop-picker/android/build/.transforms/e23c948fa969bd4663f8552b27cbfb55/results.bin
new file mode 100644
index 0000000..28bb879
--- /dev/null
+++ b/node_modules/react-native-image-crop-picker/android/build/.transforms/e23c948fa969bd4663f8552b27cbfb55/results.bin
@@ -0,0 +1 @@
+o/classes.jar
diff --git a/node_modules/react-native-image-crop-picker/android/build/.transforms/e23c948fa969bd4663f8552b27cbfb55/transformed/classes.jar b/node_modules/react-native-image-crop-picker/android/build/.transforms/e23c948fa969bd4663f8552b27cbfb55/transformed/classes.jar
new file mode 100644
index 0000000..6d70ef5
Binary files /dev/null and b/node_modules/react-native-image-crop-picker/android/build/.transforms/e23c948fa969bd4663f8552b27cbfb55/transformed/classes.jar differ
diff --git a/node_modules/react-native-image-crop-picker/android/build/generated/source/buildConfig/debug/com/reactnative/ivpusic/imagepicker/BuildConfig.java b/node_modules/react-native-image-crop-picker/android/build/generated/source/buildConfig/debug/com/reactnative/ivpusic/imagepicker/BuildConfig.java
new file mode 100644
index 0000000..74a36dd
--- /dev/null
+++ b/node_modules/react-native-image-crop-picker/android/build/generated/source/buildConfig/debug/com/reactnative/ivpusic/imagepicker/BuildConfig.java
@@ -0,0 +1,10 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package com.reactnative.ivpusic.imagepicker;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = Boolean.parseBoolean("true");
+  public static final String LIBRARY_PACKAGE_NAME = "com.reactnative.ivpusic.imagepicker";
+  public static final String BUILD_TYPE = "debug";
+}
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml b/node_modules/react-native-image-crop-picker/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
new file mode 100644
index 0000000..3a0859d
--- /dev/null
+++ b/node_modules/react-native-image-crop-picker/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/AndroidManifest.xml
@@ -0,0 +1,33 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.reactnative.ivpusic.imagepicker" >
+
+    <uses-sdk android:minSdkVersion="24" />
+
+    <queries>
+        <intent>
+            <action android:name="android.media.action.IMAGE_CAPTURE" />
+        </intent>
+    </queries>
+
+    <uses-permission
+        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
+        android:maxSdkVersion="29" />
+
+    <application>
+        <provider
+            android:name="com.reactnative.ivpusic.imagepicker.IvpusicImagePickerFileProvider"
+            android:authorities="dollar_openBracket_applicationId_closeBracket.provider"
+            android:exported="false"
+            android:grantUriPermissions="true" >
+            <meta-data
+                android:name="android.support.FILE_PROVIDER_PATHS"
+                android:resource="@xml/ivpusic_imagepicker_provider_paths" />
+        </provider>
+
+        <activity
+            android:name="com.yalantis.ucrop.UCropActivity"
+            android:theme="@style/UCropTheme" />
+    </application>
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json b/node_modules/react-native-image-crop-picker/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
new file mode 100644
index 0000000..a34b0f9
--- /dev/null
+++ b/node_modules/react-native-image-crop-picker/android/build/intermediates/aapt_friendly_merged_manifests/debug/aapt/output-metadata.json
@@ -0,0 +1,18 @@
+{
+  "version": 3,
+  "artifactType": {
+    "type": "AAPT_FRIENDLY_MERGED_MANIFESTS",
+    "kind": "Directory"
+  },
+  "applicationId": "com.reactnative.ivpusic.imagepicker",
+  "variantName": "debug",
+  "elements": [
+    {
+      "type": "SINGLE",
+      "filters": [],
+      "attributes": [],
+      "outputFile": "AndroidManifest.xml"
+    }
+  ],
+  "elementType": "File"
+}
\ No newline at end of file
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/aar_main_jar/debug/classes.jar b/node_modules/react-native-image-crop-picker/android/build/intermediates/aar_main_jar/debug/classes.jar
new file mode 100644
index 0000000..57915ac
Binary files /dev/null and b/node_modules/react-native-image-crop-picker/android/build/intermediates/aar_main_jar/debug/classes.jar differ
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/aar_metadata/debug/aar-metadata.properties b/node_modules/react-native-image-crop-picker/android/build/intermediates/aar_metadata/debug/aar-metadata.properties
new file mode 100644
index 0000000..776557e
--- /dev/null
+++ b/node_modules/react-native-image-crop-picker/android/build/intermediates/aar_metadata/debug/aar-metadata.properties
@@ -0,0 +1,5 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minCompileSdkExtension=0
+minAndroidGradlePluginVersion=1.0.0
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json b/node_modules/react-native-image-crop-picker/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/react-native-image-crop-picker/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/annotations_typedef_file/debug/typedefs.txt b/node_modules/react-native-image-crop-picker/android/build/intermediates/annotations_typedef_file/debug/typedefs.txt
new file mode 100644
index 0000000..e69de29
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/compile_library_classes_jar/debug/classes.jar b/node_modules/react-native-image-crop-picker/android/build/intermediates/compile_library_classes_jar/debug/classes.jar
new file mode 100644
index 0000000..82c8d82
Binary files /dev/null and b/node_modules/react-native-image-crop-picker/android/build/intermediates/compile_library_classes_jar/debug/classes.jar differ
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/compile_r_class_jar/debug/R.jar b/node_modules/react-native-image-crop-picker/android/build/intermediates/compile_r_class_jar/debug/R.jar
new file mode 100644
index 0000000..1a8fdf4
Binary files /dev/null and b/node_modules/react-native-image-crop-picker/android/build/intermediates/compile_r_class_jar/debug/R.jar differ
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/compile_symbol_list/debug/R.txt b/node_modules/react-native-image-crop-picker/android/build/intermediates/compile_symbol_list/debug/R.txt
new file mode 100644
index 0000000..4ce99ba
--- /dev/null
+++ b/node_modules/react-native-image-crop-picker/android/build/intermediates/compile_symbol_list/debug/R.txt
@@ -0,0 +1,2 @@
+int style UCropTheme 0x0
+int xml ivpusic_imagepicker_provider_paths 0x0
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/compiled_local_resources/debug/out/xml_ivpusic_imagepicker_provider_paths.xml.flat b/node_modules/react-native-image-crop-picker/android/build/intermediates/compiled_local_resources/debug/out/xml_ivpusic_imagepicker_provider_paths.xml.flat
new file mode 100644
index 0000000..8fa6100
Binary files /dev/null and b/node_modules/react-native-image-crop-picker/android/build/intermediates/compiled_local_resources/debug/out/xml_ivpusic_imagepicker_provider_paths.xml.flat differ
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/incremental/debug-mergeJavaRes/merge-state b/node_modules/react-native-image-crop-picker/android/build/intermediates/incremental/debug-mergeJavaRes/merge-state
new file mode 100644
index 0000000..1c983fc
Binary files /dev/null and b/node_modules/react-native-image-crop-picker/android/build/intermediates/incremental/debug-mergeJavaRes/merge-state differ
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties b/node_modules/react-native-image-crop-picker/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
new file mode 100644
index 0000000..7d00f49
--- /dev/null
+++ b/node_modules/react-native-image-crop-picker/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
@@ -0,0 +1,2 @@
+#Wed Aug 20 15:05:37 IST 2025
+com.reactnative.ivpusic.imagepicker.react-native-image-crop-picker-main-6\:/xml/ivpusic_imagepicker_provider_paths.xml=/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/build/intermediates/packaged_res/debug/xml/ivpusic_imagepicker_provider_paths.xml
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/incremental/debug/packageDebugResources/merged.dir/values/values.xml b/node_modules/react-native-image-crop-picker/android/build/intermediates/incremental/debug/packageDebugResources/merged.dir/values/values.xml
new file mode 100644
index 0000000..b4ac309
--- /dev/null
+++ b/node_modules/react-native-image-crop-picker/android/build/intermediates/incremental/debug/packageDebugResources/merged.dir/values/values.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<resources>
+    <style name="UCropTheme" parent="Theme.AppCompat.Light.NoActionBar">
+        <item name="android:windowIsFloating">true</item>
+        <item name="android:windowMinWidthMajor">100%</item>
+        <item name="android:windowMinWidthMinor">100%</item>
+        <item name="android:windowContentOverlay">@null</item>
+    </style>
+</resources>
\ No newline at end of file
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml b/node_modules/react-native-image-crop-picker/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
new file mode 100644
index 0000000..4429b4d
--- /dev/null
+++ b/node_modules/react-native-image-crop-picker/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
@@ -0,0 +1,7 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/res"><file path="/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/res/values/styles.xml" qualifiers=""><style name="UCropTheme" parent="Theme.AppCompat.Light.NoActionBar">
+        <item name="android:windowIsFloating">true</item>
+        <item name="android:windowMinWidthMajor">100%</item>
+        <item name="android:windowMinWidthMinor">100%</item>
+        <item name="android:windowContentOverlay">@null</item>
+    </style></file><file name="ivpusic_imagepicker_provider_paths" path="/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/res/xml/ivpusic_imagepicker_provider_paths.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/debug/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/build/generated/res/resValues/debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/build/generated/res/resValues/debug"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml b/node_modules/react-native-image-crop-picker/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
new file mode 100644
index 0000000..7398f37
--- /dev/null
+++ b/node_modules/react-native-image-crop-picker/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/jniLibs"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/debug/jniLibs"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/incremental/mergeDebugShaders/merger.xml b/node_modules/react-native-image-crop-picker/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
new file mode 100644
index 0000000..2ac1bd5
--- /dev/null
+++ b/node_modules/react-native-image-crop-picker/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/shaders"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/debug/shaders"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/incremental/packageDebugAssets/merger.xml b/node_modules/react-native-image-crop-picker/android/build/intermediates/incremental/packageDebugAssets/merger.xml
new file mode 100644
index 0000000..53948b3
--- /dev/null
+++ b/node_modules/react-native-image-crop-picker/android/build/intermediates/incremental/packageDebugAssets/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/debug/assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/build/intermediates/shader_assets/debug/out"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/BuildConfig.class b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/BuildConfig.class
new file mode 100644
index 0000000..8d51143
Binary files /dev/null and b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/BuildConfig.class differ
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/Compression.class b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/Compression.class
new file mode 100644
index 0000000..8983e8a
Binary files /dev/null and b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/Compression.class differ
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/ExifExtractor.class b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/ExifExtractor.class
new file mode 100644
index 0000000..39904dc
Binary files /dev/null and b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/ExifExtractor.class differ
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/GeoDegree.class b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/GeoDegree.class
new file mode 100644
index 0000000..cd9bdba
Binary files /dev/null and b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/GeoDegree.class differ
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/IvpusicImagePickerFileProvider.class b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/IvpusicImagePickerFileProvider.class
new file mode 100644
index 0000000..d7d59de
Binary files /dev/null and b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/IvpusicImagePickerFileProvider.class differ
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/PickerModule$1.class b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/PickerModule$1.class
new file mode 100644
index 0000000..627700f
Binary files /dev/null and b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/PickerModule$1.class differ
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/PickerModule$2.class b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/PickerModule$2.class
new file mode 100644
index 0000000..146c55f
Binary files /dev/null and b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/PickerModule$2.class differ
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/PickerModule$3.class b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/PickerModule$3.class
new file mode 100644
index 0000000..4ac738d
Binary files /dev/null and b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/PickerModule$3.class differ
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/PickerModule$4.class b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/PickerModule$4.class
new file mode 100644
index 0000000..3ed5b1a
Binary files /dev/null and b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/PickerModule$4.class differ
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/PickerModule$5.class b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/PickerModule$5.class
new file mode 100644
index 0000000..8afae1e
Binary files /dev/null and b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/PickerModule$5.class differ
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/PickerModule$6.class b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/PickerModule$6.class
new file mode 100644
index 0000000..8a39a8e
Binary files /dev/null and b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/PickerModule$6.class differ
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/PickerModule$7$1.class b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/PickerModule$7$1.class
new file mode 100644
index 0000000..94a986b
Binary files /dev/null and b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/PickerModule$7$1.class differ
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/PickerModule$7$2.class b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/PickerModule$7$2.class
new file mode 100644
index 0000000..5429444
Binary files /dev/null and b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/PickerModule$7$2.class differ
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/PickerModule$7.class b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/PickerModule$7.class
new file mode 100644
index 0000000..6de9e18
Binary files /dev/null and b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/PickerModule$7.class differ
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/PickerModule.class b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/PickerModule.class
new file mode 100644
index 0000000..667394a
Binary files /dev/null and b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/PickerModule.class differ
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/PickerPackage.class b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/PickerPackage.class
new file mode 100644
index 0000000..d0ef52f
Binary files /dev/null and b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/PickerPackage.class differ
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/RealPathUtil.class b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/RealPathUtil.class
new file mode 100644
index 0000000..e229606
Binary files /dev/null and b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/RealPathUtil.class differ
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/ResultCollector.class b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/ResultCollector.class
new file mode 100644
index 0000000..068c812
Binary files /dev/null and b/node_modules/react-native-image-crop-picker/android/build/intermediates/javac/debug/classes/com/reactnative/ivpusic/imagepicker/ResultCollector.class differ
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/local_only_symbol_list/debug/R-def.txt b/node_modules/react-native-image-crop-picker/android/build/intermediates/local_only_symbol_list/debug/R-def.txt
new file mode 100644
index 0000000..6dd29f4
--- /dev/null
+++ b/node_modules/react-native-image-crop-picker/android/build/intermediates/local_only_symbol_list/debug/R-def.txt
@@ -0,0 +1,4 @@
+R_DEF: Internal format may change without notice
+local
+style UCropTheme
+xml ivpusic_imagepicker_provider_paths
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt b/node_modules/react-native-image-crop-picker/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
new file mode 100644
index 0000000..d1197ac
--- /dev/null
+++ b/node_modules/react-native-image-crop-picker/android/build/intermediates/manifest_merge_blame_file/debug/manifest-merger-blame-debug-report.txt
@@ -0,0 +1,52 @@
+1<?xml version="1.0" encoding="utf-8"?>
+2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+3    package="com.reactnative.ivpusic.imagepicker" >
+4
+5    <uses-sdk android:minSdkVersion="24" />
+6
+7    <queries>
+7-->/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:4:5-8:15
+8        <intent>
+8-->/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:5:9-7:18
+9            <action android:name="android.media.action.IMAGE_CAPTURE" />
+9-->/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:6:13-73
+9-->/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:6:21-70
+10        </intent>
+11    </queries>
+12
+13    <uses-permission
+13-->/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:10:5-11:38
+14        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
+14-->/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:10:22-78
+15        android:maxSdkVersion="29" />
+15-->/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:11:9-35
+16
+17    <application>
+17-->/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:13:5-28:19
+18        <provider
+18-->/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:15:9-23:20
+19            android:name="com.reactnative.ivpusic.imagepicker.IvpusicImagePickerFileProvider"
+19-->/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:16:13-59
+20            android:authorities="${applicationId}.provider"
+20-->/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:17:13-60
+21            android:exported="false"
+21-->/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:18:13-37
+22            android:grantUriPermissions="true" >
+22-->/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:19:13-47
+23            <meta-data
+23-->/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:20:13-22:78
+24                android:name="android.support.FILE_PROVIDER_PATHS"
+24-->/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:21:17-67
+25                android:resource="@xml/ivpusic_imagepicker_provider_paths" />
+25-->/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:22:17-75
+26        </provider>
+27
+28        <activity
+28-->/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:25:9-27:49
+29            android:name="com.yalantis.ucrop.UCropActivity"
+29-->/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:26:13-60
+30            android:theme="@style/UCropTheme" />
+30-->/Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:27:13-46
+31    </application>
+32
+33</manifest>
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/merged_java_res/debug/feature-react-native-image-crop-picker.jar b/node_modules/react-native-image-crop-picker/android/build/intermediates/merged_java_res/debug/feature-react-native-image-crop-picker.jar
new file mode 100644
index 0000000..15cb0ec
Binary files /dev/null and b/node_modules/react-native-image-crop-picker/android/build/intermediates/merged_java_res/debug/feature-react-native-image-crop-picker.jar differ
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml b/node_modules/react-native-image-crop-picker/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml
new file mode 100644
index 0000000..7d70629
--- /dev/null
+++ b/node_modules/react-native-image-crop-picker/android/build/intermediates/merged_manifest/debug/AndroidManifest.xml
@@ -0,0 +1,33 @@
+<?xml version="1.0" encoding="utf-8"?>
+<manifest xmlns:android="http://schemas.android.com/apk/res/android"
+    package="com.reactnative.ivpusic.imagepicker" >
+
+    <uses-sdk android:minSdkVersion="24" />
+
+    <queries>
+        <intent>
+            <action android:name="android.media.action.IMAGE_CAPTURE" />
+        </intent>
+    </queries>
+
+    <uses-permission
+        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
+        android:maxSdkVersion="29" />
+
+    <application>
+        <provider
+            android:name="com.reactnative.ivpusic.imagepicker.IvpusicImagePickerFileProvider"
+            android:authorities="${applicationId}.provider"
+            android:exported="false"
+            android:grantUriPermissions="true" >
+            <meta-data
+                android:name="android.support.FILE_PROVIDER_PATHS"
+                android:resource="@xml/ivpusic_imagepicker_provider_paths" />
+        </provider>
+
+        <activity
+            android:name="com.yalantis.ucrop.UCropActivity"
+            android:theme="@style/UCropTheme" />
+    </application>
+
+</manifest>
\ No newline at end of file
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/navigation_json/debug/navigation.json b/node_modules/react-native-image-crop-picker/android/build/intermediates/navigation_json/debug/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/react-native-image-crop-picker/android/build/intermediates/navigation_json/debug/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/packaged_res/debug/values/values.xml b/node_modules/react-native-image-crop-picker/android/build/intermediates/packaged_res/debug/values/values.xml
new file mode 100644
index 0000000..b4ac309
--- /dev/null
+++ b/node_modules/react-native-image-crop-picker/android/build/intermediates/packaged_res/debug/values/values.xml
@@ -0,0 +1,9 @@
+<?xml version="1.0" encoding="utf-8"?>
+<resources>
+    <style name="UCropTheme" parent="Theme.AppCompat.Light.NoActionBar">
+        <item name="android:windowIsFloating">true</item>
+        <item name="android:windowMinWidthMajor">100%</item>
+        <item name="android:windowMinWidthMinor">100%</item>
+        <item name="android:windowContentOverlay">@null</item>
+    </style>
+</resources>
\ No newline at end of file
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/packaged_res/debug/xml/ivpusic_imagepicker_provider_paths.xml b/node_modules/react-native-image-crop-picker/android/build/intermediates/packaged_res/debug/xml/ivpusic_imagepicker_provider_paths.xml
new file mode 100644
index 0000000..ffa74ab
--- /dev/null
+++ b/node_modules/react-native-image-crop-picker/android/build/intermediates/packaged_res/debug/xml/ivpusic_imagepicker_provider_paths.xml
@@ -0,0 +1,4 @@
+<?xml version="1.0" encoding="utf-8"?>
+<paths xmlns:android="http://schemas.android.com/apk/res/android">
+    <external-path name="external_files" path="."/>
+</paths>
\ No newline at end of file
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar b/node_modules/react-native-image-crop-picker/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar
new file mode 100644
index 0000000..a627084
Binary files /dev/null and b/node_modules/react-native-image-crop-picker/android/build/intermediates/runtime_library_classes_jar/debug/classes.jar differ
diff --git a/node_modules/react-native-image-crop-picker/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt b/node_modules/react-native-image-crop-picker/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt
new file mode 100644
index 0000000..cbf1c8d
--- /dev/null
+++ b/node_modules/react-native-image-crop-picker/android/build/intermediates/symbol_list_with_package_name/debug/package-aware-r.txt
@@ -0,0 +1,3 @@
+com.reactnative.ivpusic.imagepicker
+style UCropTheme
+xml ivpusic_imagepicker_provider_paths
diff --git a/node_modules/react-native-image-crop-picker/android/build/outputs/aar/react-native-image-crop-picker-debug.aar b/node_modules/react-native-image-crop-picker/android/build/outputs/aar/react-native-image-crop-picker-debug.aar
new file mode 100644
index 0000000..fcf2820
Binary files /dev/null and b/node_modules/react-native-image-crop-picker/android/build/outputs/aar/react-native-image-crop-picker-debug.aar differ
diff --git a/node_modules/react-native-image-crop-picker/android/build/outputs/logs/manifest-merger-debug-report.txt b/node_modules/react-native-image-crop-picker/android/build/outputs/logs/manifest-merger-debug-report.txt
new file mode 100644
index 0000000..2e6a900
--- /dev/null
+++ b/node_modules/react-native-image-crop-picker/android/build/outputs/logs/manifest-merger-debug-report.txt
@@ -0,0 +1,55 @@
+-- Merging decision tree log ---
+manifest
+ADDED from /Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:1:1-30:12
+INJECTED from /Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:1:1-30:12
+	package
+		ADDED from /Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:2:5-50
+		INJECTED from /Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml
+	xmlns:android
+		ADDED from /Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:1:11-69
+queries
+ADDED from /Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:4:5-8:15
+intent#action:name:android.media.action.IMAGE_CAPTURE
+ADDED from /Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:5:9-7:18
+action#android.media.action.IMAGE_CAPTURE
+ADDED from /Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:6:13-73
+	android:name
+		ADDED from /Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:6:21-70
+uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
+ADDED from /Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:10:5-11:38
+	android:maxSdkVersion
+		ADDED from /Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:11:9-35
+	android:name
+		ADDED from /Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:10:22-78
+application
+ADDED from /Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:13:5-28:19
+provider#com.reactnative.ivpusic.imagepicker.IvpusicImagePickerFileProvider
+ADDED from /Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:15:9-23:20
+	android:grantUriPermissions
+		ADDED from /Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:19:13-47
+	android:authorities
+		ADDED from /Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:17:13-60
+	android:exported
+		ADDED from /Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:18:13-37
+	android:name
+		ADDED from /Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:16:13-59
+meta-data#android.support.FILE_PROVIDER_PATHS
+ADDED from /Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:20:13-22:78
+	android:resource
+		ADDED from /Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:22:17-75
+	android:name
+		ADDED from /Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:21:17-67
+activity#com.yalantis.ucrop.UCropActivity
+ADDED from /Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:25:9-27:49
+	android:theme
+		ADDED from /Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:27:13-46
+	android:name
+		ADDED from /Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml:26:13-60
+uses-sdk
+INJECTED from /Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml reason: use-sdk injection requested
+INJECTED from /Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml
+INJECTED from /Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml
+	android:targetSdkVersion
+		INJECTED from /Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml
+	android:minSdkVersion
+		INJECTED from /Users/<USER>/Desktop/Projects/footbizz-app/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml
diff --git a/node_modules/react-native-image-crop-picker/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin b/node_modules/react-native-image-crop-picker/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin
new file mode 100644
index 0000000..b4f25a4
Binary files /dev/null and b/node_modules/react-native-image-crop-picker/android/build/tmp/compileDebugJavaWithJavac/previous-compilation-data.bin differ
diff --git a/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml b/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml
index 391f303..231ad8a 100644
--- a/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml
+++ b/node_modules/react-native-image-crop-picker/android/src/main/AndroidManifest.xml
@@ -24,7 +24,7 @@
 
         <activity
             android:name="com.yalantis.ucrop.UCropActivity"
-            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />
+            android:theme="@style/UCropTheme" />
     </application>
 
 </manifest>
diff --git a/node_modules/react-native-image-crop-picker/android/src/main/res/values/styles.xml b/node_modules/react-native-image-crop-picker/android/src/main/res/values/styles.xml
new file mode 100644
index 0000000..149a710
--- /dev/null
+++ b/node_modules/react-native-image-crop-picker/android/src/main/res/values/styles.xml
@@ -0,0 +1,8 @@
+<resources>
+    <style name="UCropTheme" parent="Theme.AppCompat.Light.NoActionBar">
+        <item name="android:windowIsFloating">true</item>
+        <item name="android:windowMinWidthMajor">100%</item>
+        <item name="android:windowMinWidthMinor">100%</item>
+        <item name="android:windowContentOverlay">@null</item>
+    </style>
+</resources>
\ No newline at end of file
