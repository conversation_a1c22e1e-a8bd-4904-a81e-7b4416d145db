import { images } from "./images";

export const fileTypes = {
  "image/jpeg": "JPEG",
  "image/png": "PNG",
  "application/pdf": "PDF",
  "text/plain": "TXT",
  "application/msword": "DOC",
  "application/vnd.ms-excel": "XLS",
  "application/vnd.ms-powerpoint": "PPT",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
    "DOCX",
  "application/vnd.openxmlformats-officedocument.presentationml.presentation":
    "PPTX",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "XLSX",
};

export const languageArr = [
  { lang_name: "English", lang_code: "en", isRTL: false },
];

export const signUpListArr = [
  {
    id: 2,
    text: "signUpWithAppleText",
    img: images.appleIcon,
  },
  {
    id: 1,
    text: "signUpWithGoogleText",
    img: images.googleIcon,
  },
  {
    id: 3,
    text: "signUpWithFaceBookText",
    img: images.facebookIcon,
  },
  {
    id: 4,
    text: "signUpWithMobileNoText",
    img: images.mobileIcon,
  },
  {
    id: 5,
    text: "signUpWithEmailText",
    img: images.msgIcon,
  },
];
export const LoginListArr = [
  {
    id: 2,
    text: "signInWithAppleText",
    img: images.appleIcon,
  },
  {
    id: 1,
    text: "signInWithGoogleText",
    img: images.googleIcon,
  },
  {
    id: 3,
    text: "signInWithFaceBookText",
    img: images.facebookIcon,
  },
  {
    id: 4,
    text: "signInWithMobileNoText",
    img: images.mobileIcon,
  },
  {
    id: 5,
    text: "signInWithEmailText",
    img: images.msgIcon,
  },
];
export const genderData = [
  { label: "Male", value: "male" },
  { label: "Female", value: "female" },
  { label: "Non binary", value: "non-binary" },
  { label: "Prefer not to say", value: "preferNotToSay" },
];

export const callOfActionArr = [
  { label: "Send Message", value: "send_message" },
  { label: "Contact Now", value: "contact_now" },
  { label: "Visit My Profile", value: "visit_my_profile" },
];

export const followListData = [
  {
    id: 1,
    profileDP:
      "https://i.pinimg.com/564x/de/6e/8d/de6e8d53598eecfb6a2d86919b267791.jpg",
    userName: "Yash",
  },
  {
    id: 2,
    profileDP:
      "https://i.pinimg.com/564x/de/6e/8d/de6e8d53598eecfb6a2d86919b267791.jpg",
    userName: "Yash",
  },
  {
    id: 3,
    profileDP:
      "https://i.pinimg.com/564x/de/6e/8d/de6e8d53598eecfb6a2d86919b267791.jpg",
    userName: "Yash",
  },
  {
    id: 4,
    profileDP:
      "https://i.pinimg.com/564x/de/6e/8d/de6e8d53598eecfb6a2d86919b267791.jpg",
    userName: "Yash",
  },
  {
    id: 5,
    profileDP:
      "https://i.pinimg.com/564x/de/6e/8d/de6e8d53598eecfb6a2d86919b267791.jpg",
    userName: "Yash",
  },
  {
    id: 6,
    profileDP:
      "https://i.pinimg.com/564x/de/6e/8d/de6e8d53598eecfb6a2d86919b267791.jpg",
    userName: "Yash",
  },
  {
    id: 7,
    profileDP:
      "https://i.pinimg.com/564x/de/6e/8d/de6e8d53598eecfb6a2d86919b267791.jpg",
    userName: "Yash",
  },
  {
    id: 8,
    profileDP:
      "https://i.pinimg.com/564x/de/6e/8d/de6e8d53598eecfb6a2d86919b267791.jpg",
    userName: "Yash",
  },
  {
    id: 9,
    profileDP:
      "https://i.pinimg.com/564x/de/6e/8d/de6e8d53598eecfb6a2d86919b267791.jpg",
    userName: "Yash",
  },
  {
    id: 10,
    profileDP:
      "https://i.pinimg.com/564x/de/6e/8d/de6e8d53598eecfb6a2d86919b267791.jpg",
    userName: "Yash",
  },
  {
    id: 11,
    profileDP:
      "https://i.pinimg.com/564x/de/6e/8d/de6e8d53598eecfb6a2d86919b267791.jpg",
    userName: "Yash",
  },
  {
    id: 12,
    profileDP:
      "https://i.pinimg.com/564x/de/6e/8d/de6e8d53598eecfb6a2d86919b267791.jpg",
    userName: "Yash",
  },
  {
    id: 13,
    profileDP:
      "https://i.pinimg.com/564x/de/6e/8d/de6e8d53598eecfb6a2d86919b267791.jpg",
    userName: "Yash",
  },
  {
    id: 14,
    profileDP:
      "https://i.pinimg.com/564x/de/6e/8d/de6e8d53598eecfb6a2d86919b267791.jpg",
    userName: "Yash",
  },
  {
    id: 15,
    profileDP:
      "https://i.pinimg.com/564x/de/6e/8d/de6e8d53598eecfb6a2d86919b267791.jpg",
    userName: "Yash",
  },
];

export const menuSearchData = [
  {
    name: "For You",
    id: 1,
  },
  {
    name: "Accounts",
    id: 2,
  },
  {
    name: "Reels",
    id: 3,
  },
  {
    name: "Groups",
    id: 3,
  },
];

export const chatList = {
  data: [
    {
      id: 1,
      userName: "Chetan Parmar",
      message: "Hello this is my test message",
      dateAndTime: "20/03/2005",
      count: 5,
      userImg: "https://picsum.photos/200/300",
      isOnline: 1,
      tickStatus: "singleGray",
    },
    {
      id: 2,
      userName: "John Doe",
      message: "Hey there!",
      dateAndTime: "21/03/2005",
      count: 2,
      userImg: "https://picsum.photos/200/301",
      isOnline: 0,
      tickStatus: "doubleBlue",
    },
    {
      id: 3,
      userName: "Alice Smith",
      message: "Good morning!",
      dateAndTime: "22/03/2005",
      count: 0,
      userImg: "https://picsum.photos/200/302",
      isOnline: 1,
      tickStatus: "singleGray",
    },
    {
      id: 4,
      userName: "Bob Johnson",
      message: "How are you?",
      dateAndTime: "23/03/2005",
      count: 3,
      userImg: "https://picsum.photos/200/303",
      isOnline: 0,
      tickStatus: "doubleGray",
    },
    {
      id: 5,
      userName: "Emily Wilson",
      message: "See you later!",
      dateAndTime: "24/03/2005",
      count: 1,
      userImg: "https://picsum.photos/200/304",
      isOnline: 1,
      tickStatus: "doubleBlue",
    },
  ],
};

export const moreInfo = [
  {
    id: 1,
    title: "add_story_text",
  },
  {
    id: 2,
    title: "saveText",
  },
  {
    id: 3,
    title: "sendMessageText",
  },
  {
    id: 4,
    title: "removeFollowText",
  },
  {
    id: 5,
    title: "reportText",
  },
];
export const moreInfoUnSaved = [
  {
    id: 1,
    title: "add_story_text",
  },
  {
    id: 2,
    title: "unSaveText",
  },
  {
    id: 3,
    title: "sendMessageText",
  },
  {
    id: 4,
    title: "removeFollowText",
  },
  {
    id: 5,
    title: "reportText",
  },
];

export const selfUserForDataForUnSave = [
  {
    id: 1,
    title: "add_story_text",
  },
  {
    id: 2,
    title: "unSaveText",
  },
  {
    id: 6,
    title: "delete",
    color: "#FF0000",
  },
];

export const selfUserForDataForSave = [
  {
    id: 1,
    title: "add_story_text",
  },
  {
    id: 2,
    title: "saveText",
  },
  {
    id: 6,
    title: "delete",
    color: "#FF0000",
  },
];

export const reportDataArr = [
  {
    id: 1,
    options: "harassmentText",
  },
  {
    id: 2,
    options: "bullyingText",
  },
  {
    id: 3,
    options: "spamText",
  },
  {
    id: 4,
    options: "somethingElseText",
  },
];

export const postMoreInfo = [
  {
    id: 1,
    title: "unFollowText",
  },
  {
    id: 2,
    title: "reportText",
  },
  {
    id: 3,
    title: "delete",
    color: "#FF0000",
  },
];

export const profileTabBar = [
  {
    id: 1,
    title: "Post",
  },
  {
    id: 2,
    title: "Reel",
  },
  {
    id: 3,
    title: "Boosted Post",
  },
];

export const myIntroOptionData = [
  {
    key: "upDateIntro",
    // icon: "material-symbols_block",
    text: "Update Intro",
  },
  { key: "delete", text: "Delete" },
];

export const profileEditModalItem = [
  {
    id: 1,
    title: "Edit Profile",
  },
  {
    id: 2,
    title: "Share This Profile",
  },
];
export const optionMenuData = [
  {
    id: 1,
    title: "Make as Admin",
  },
  {
    id: 2,
    title: "Remove User",
  },
];
export const profileEditForAnotherUserModalItem = [
  {
    id: 1,
    title: "Share This Profile",
  },
  {
    id: 2,
    title: "Report",
  },
];

export const highLightOptionData = [
  {
    key: "rename",
    text: "Rename",
  },

  {
    key: "remove",
    text: "Remove",
  },
];

export const overViewDesc = [
  {
    id: 1,
    title: "Accounts reached",
    desc: "The number of unique accounts that have seen this post, at least once. Reach is different from impressions, which may include multiple views of your post by the same accounts. This metric is estimated.",
  },
  {
    id: 2,
    title: "Followers",
    desc: "The number of followers that have seen your post.",
  },
  {
    id: 3,
    title: "Non-followers",
    desc: "The number of non-followers that have seen your post.",
  },
  {
    id: 4,
    title: "Impressions",
    desc: "The number of times your post was on screen.",
  },
];
