import { createGroup } from "@screens/Chat/apiCallFunction";
import Config from "react-native-config"; // Fetch data from .env

const isLive = Config?.NODE_ENV === "production";
const isStaging = Config?.NODE_ENV === "staging";

// const baseUrl = isLive
//   ? Config?.LIVE_API_URL
//   : isStaging
//     ? Config?.STAGING_API_URL
//     : Config?.LIVE_API_URL;
// const baseUrl = "http://*************:9000/";
const baseUrl = "https://staging-api.footbizz.in/";

const BaseSetting = {
  name: "footbizz",
  displayName: "footbizz",
  appVersionCode: "1",
  // bugsnagApiKey: '',
  baseUrl,
  api: `${baseUrl}`,
  shareEndPoint: `${baseUrl}`,
  socketURL: `${baseUrl}`, // local ip
  // socketURL: 'http://*************:1337/', // server domain
  timeOut: 30000,
  MAPS_API_CALL_KEY: "",
  MAPS_URL: "https://maps.googleapis.com/maps/api/place",
  googleLoginClientId: "",
  googleAutoCompleteKey: "",
  googleClientId: Config?.GOOGLE_CLIENT_ID,
  giphyAndroidApiKey: "8kA2zhjLyuFK3XtP8xibK6Ik0QJRFxOQ", // Viral's account key
  giphyIosApiKey: "l9EszkNJTGmkVY5pwhXiqVQSamzyL15B", // Client's account key
  razorPayKey: "rzp_test_pPTVdFlHrwTpmw", // Test key
  endpoints: {
    login: "v1/user/login",
    signUp: "v1/user/signup",
    verifyOtp: "v1/user/verify-otp",
    resendOtp: "v1/user/generate-otp",
    userProfile: "v1/user/create-user-profile",
    companyProfile: "v1/user/create-company-profile",
    commonData: "v1/common/get-common-data",
    forgotPass: "v1/user/forgot-password",
    updatePass: "v1/user/update-password",
    fileUpload: "v1/common/upload",
    getUserData: "v1/user/get-data/",
    UpdateUserData: "v1/user/update",
    changePass: "v1/user/change-password",
    addUserStory: "v1/user-stories/add",
    viewUserStory: "v1/user-stories/list",
    userViewStory: "v1/user-stories/view",
    contactUs: "v1/user/contact-admin",
    DeleteAccount: "v1/user/delete-account",
    logout: "v1/user/logout",
    userStoryLike: "v1/user-stories/like-dislike",
    createPost: "v1/post/create",
    postList: "v1/post/list",
    likeDislike: "v1/post/like-dislike/",
    sendFcmToken: "v1/user/add-fcm",
    commentsAdd: "v1/comments/add",
    commentLike: "v1/comments/like/",
    getReelsList: "v1/reel/list",
    followHandler: "v1/follow/connect",
    followList: "v1/follow/get-data",
    createReel: "v1/reel/create",
    reelsLikeDislike: "v1/reel/like-dislike",
    commentsHide: "v1/comments/hide",
    reelsCommentList: "v1/comments/list",
    saveUnsavedPost: "v1/post/save/",
    saveUnSavedReel: "v1/reel/save",
    reportUser: "v1/user/report",
    viewReel: "v1/reel/view",
    subscriptionPlan: "v1/user/list-plan",
    addOnPlan: "v1/user/list-add-on-plan",
    addReferralCode: "v1/user/verify-code",
    purchasePlan: "v1/user/purchase",
    cancelPlan: "v1/user/cancel-plan",
    purchaseAddOnPlan: "v1/user/purchase-add-on",
    savedPostList: "v1/post/get-post",
    storyHistory: "v1/user-stories/story-history",
    addHighlight: "v1/user-stories/add-to-highlight",
    addIntro: "v1/user/add-my-intro",
    myHighlights: "v1/user-stories/my-highlights",
    getMyIntro: "v1/user/get-my-intro",
    getPostDetail: "v1/post/list-by-user",
    getReelDetail: "v1/reel/list-by-user",
    deleteIntro: "v1/user/remove-intro",
    likeList: "v1/reel/liked",
    getCompanyDetail: "v1/user/company-list",
    highlightUpdate: "v1/user-stories/highlight-rename",
    removeHighlight: "v1/user-stories/highlight-delete",
    getFriendUserData: "v1/user/get-user-by-id",
    searchChatList: "v1/user/chat-user-search",
    blockUnblock: "v1/user/block/",
    getUserPlan: "v1/user/get-my-plan",
    postLikeList: "v1/post/liked-user",
    blockList: "v1/user/block-list",
    clearChat: "v1/user/clear-chat/",
    sharePost: "v1/user/share-post",
    clearChatList: "v1/user/remove-chat/",
    shareProfile: "v1/user/share-profile",
    storyReply: "v1/user-stories/reply",
    estiMatePeople: "v1/user/estimate-people",
    boostPost: "v1/post/boost",
    verifyBoostPayment: "v1/post/validate-payment",
    verifyAddOnPayment: "v1/user/validate-add-on",
    searchList: "v1/user/explore-search",
    boostedPostList: "v1/post/list",
    getBoostAnalytics: "v1/user/get-analytics",
    reachPostCount: "v1/user/reach",
    notificationList: "v1/user/notification",
    deleteNotification: "v1/user/delete-notification",
    deletePostOrReel: "v1/user/delete-posts",
    readNotification: "v1/user/read-notification",
    paymentHistory: "v1/user/payment-history",
    deleteStory: "v1/user-stories/delete",
    addRating: "v1/user/add-rates",
    createBoostPaymentLink: "v2/user/cashFree-payment",
    getCouponDetail: "admin/membership-plan/plan-details",
    getSpecialOffer: "admin/membership-plan/active-special-plan",
    getGroupChat: "v1/user/group-list",
    createGroup: "v1/user/create-group",
    groupInfo: "v1/user/group-info",
    groupmemberList: "v1/user/group-member-list",
    addGroupMember: "v1/user/add-member",
    removerGroupmember: "v1/user/remove-member",
    makeGroupAdmin: "v1/user/make-admin",
    exitGroup: "v1/user/leave-group",
    editGroupMessage: "v1/user/edit-group-message",
    editMessage: "v1/user/edit-message",
    deleteMessage: "v1/user/delete-group-message",
    groupSearch: "v1/user/search-group",
    groupRequestList: "v1/user/group-requests",
    grouprequettoJoin: "v1/user/request-to-join-group",
    requestAction: "v1/user/handle-group-request",
    recomandedGroup: "v1/user/recommended-groups",
    sendMultipleImage: "v1/user/send-multiple-images",
    memberList: "v1/follow/search-list",
    groupUpdateIcon: "v1/user/update-group",
  },

  geolocationOptions: {
    enableHighAccuracy: false,
    timeout: 50000,
    maximumAge: 10000,
    distanceFilter: 1,
  },
  geoOptionHighAccurate: {
    enableHighAccuracy: true,
    timeout: 10000,
    maximumAge: 10000,
    distanceFilter: 1,
  },
};

export default BaseSetting;
