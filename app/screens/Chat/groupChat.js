import React, { useCallback, useEffect, useRef, useState } from "react";
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Platform,
  ScrollView,
  RefreshControl,
  KeyboardAvoidingView,
  ActivityIndicator,
} from "react-native";
import styles from "./styles";
import { isEmpty, isNull, truncate } from "lodash-es";
import { BaseColors } from "@config/theme";
import FastImage from "react-native-fast-image";
import { CustomIcon } from "@config/LoadIcons";
import { Swipeable } from "react-native-gesture-handler";
import { useFocusEffect } from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import NoRecord from "@components/NoRecord";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import MiniLoader from "@components/MiniLoader";
import {
  getGroupChatList,
  groupRequestList,
  groupSearchMessage,
} from "./apiCallFunction";
import Iicons from "react-native-vector-icons/Ionicons";
import SocketActions from "@redux/reducers/socket/actions";
import Toast from "react-native-simple-toast";
import { FontFamily } from "@config/typography";
import PurChasePlanModal from "@components/PurchasePlanModal";

const { getChatList, setTotalMsgCount, setSelectedRoom, setGroupChatList } =
  SocketActions;

const GroupChat = ({ navigation, search }) => {
  const swipeableRef = useRef(null);

  const dispatch = useDispatch();

  // state
  const [typingTimeout, setTypingTimeout] = useState(null);
  const [searchBtnData, setSearchBtnData] = useState("");
  const [isPaymentModal, setIsPaymentModal] = useState(false);
  const [searchChatList, setSearchChatList] = useState([]);
  const [addLoading, setAddLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [chatLoader, setChatLoader] = useState(false);
  const [conversationIdData, setConversationIdData] = useState("");
  const [refreshing, setRefreshing] = useState(false);
  const [gropChatList, setGropChatList] = useState([]);
  const [requestId, setRequestId] = useState("");
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const [isPaginationLoading, setIsPaginationLoading] = useState(false);

  // redux State
  const { chatListNextEnablePage, chatData, typingData, groupChat } =
    useSelector((s) => s.socket);

  const { isCurrentPlan, activePlanData } = useSelector((a) => a.auth);

  dayjs.extend(utc);

  useEffect(() => {
    setSearchBtnData(search);
  }, [search]);

  // serach input handler function
  const handleInputChange = useCallback(
    (searchBtnData) => {
      clearTimeout(typingTimeout);
      handleSearchChatList(searchBtnData, 1);
    },
    [typingTimeout, searchBtnData]
  );

  const groupChatList = async (type = "", pageNumber = 1) => {
    // Prevent multiple concurrent API calls for pagination
    if (type === "pagination" && isPaginationLoading) {
      return;
    }

    if (type === "refresh") {
      setChatLoader(false);
      setPage(1);
      setHasMore(false);
    } else if (type === "pagination") {
      setIsPaginationLoading(true);
    } else {
      setChatLoader(true);
    }

    const resp = await getGroupChatList(dispatch, pageNumber);

    if (resp?.success) {
      setChatLoader(false);
      setIsPaginationLoading(false);

      if (pageNumber === 1) {
        dispatch(SocketActions.setGroupChatList(resp.data));
      } else {
        dispatch(SocketActions.setGroupChatList([...groupChat, ...resp.data]));
      }

      // check if there is more data
      if (resp?.pagination.hasNextPage) {
        setHasMore(true);
      } else {
        setHasMore(false);
      }

      // Update page number for successful pagination
      if (type === "pagination") {
        setPage(pageNumber);
      }
    } else {
      setChatLoader(false);
      setIsPaginationLoading(false);
    }
  };

  useFocusEffect(
    useCallback(() => {
      setSearchChatList([]);
      dispatch(setTotalMsgCount(0));
      dispatch(setSelectedRoom({}));
      groupChatList();
    }, [])
  );

  useEffect(() => {
    if (
      isEmpty(isCurrentPlan) &&
      !activePlanData?.is_prime_user &&
      !activePlanData?.is_free_user
    ) {
      setIsPaymentModal(true);
    }
  }, []);

  useEffect(() => {
    if (!isNull(searchBtnData)) {
      setLoading(true);
      handleInputChange(searchBtnData);
    }
  }, [searchBtnData]);

  const handleSearchChatList = async (
    searchBtnData,
    page = 1,
    type = "",
    paginationType = ""
  ) => {
    if (type === "pagination" && isPaginationLoading) {
      return;
    }

    if (type === "request") {
      setLoading(false);
      setPage(1);
      setHasMore(false);
    } else if (paginationType === "pagination") {
      setIsPaginationLoading(true);
    } else {
      setLoading(true);
    }

    const resp = await groupSearchMessage(searchBtnData?.trim(), page);
    console.log("🚀 ~ handleSearchChatList ~ resp:", resp?.data);

    if (resp?.data?.success && !isEmpty(resp?.data?.data)) {
      setSearchChatList({
        page: page,
        next_enable: resp?.data?.pagination?.hasNextPage,
        data:
          page > 1
            ? [...searchChatList?.data, ...resp?.data?.data]
            : resp?.data?.data,
      });
      setLoading(false);
      setIsPaginationLoading(false);
      // check if there is more data
      if (resp?.data?.pagination?.hasNextPage) {
        setHasMore(true);
      } else {
        setHasMore(false);
      }

      // Update page number for successful pagination
      if (type === "pagination") {
        setPage(page);
      }
    } else {
      setSearchChatList([]);
      setLoading(false);
    }
    setLoading(false);
  };

  const handleModalVisible = (receiver_id) => {
    setConversationIdData(receiver_id);
  };

  const handleRequestToJoin = async (group_details_id) => {
    setAddLoading(true);
    const resp = await groupRequestList(group_details_id);
    if (resp?.success) {
      setAddLoading(false);
      await handleSearchChatList(
        searchBtnData,
        Number(searchChatList?.page),
        "request"
      );
    } else {
      setAddLoading(false);
    }
  };

  // Render swipeAble delete button
  const renderRightActions = (receiver_id) => {
    return (
      <View
        style={{
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <TouchableOpacity
          activeOpacity={0.8}
          style={styles.sendBtnMain}
          onPress={() => handleModalVisible(receiver_id)}
        >
          <CustomIcon name={"Delete"} size={20} color={BaseColors.white} />
        </TouchableOpacity>
      </View>
    );
  };

  const RenderDataItem = ({ item, navigation }) => {
    return (
      <TouchableOpacity
        style={styles.renderItemMainView}
        activeOpacity={0.8}
        onPress={() => {
          if (
            !isEmpty(isCurrentPlan) ||
            activePlanData?.is_prime_user ||
            activePlanData?.is_free_user
          ) {
            dispatch(setSelectedRoom(item));
            navigation.navigate("MessagesInfo", { userInfo: item });
          }
        }}
        disabled={item?.is_member === 1 ? false : true}
      >
        {/* Image */}
        <View>
          <FastImage source={{ uri: item?.imageUrl }} style={styles.imgView} />
        </View>

        {/* User Titles */}
        <View style={styles.titleView}>
          {item?.groupName ? (
            <Text style={styles.userNameViewText} numberOfLines={1}>
              {item?.groupName}
            </Text>
          ) : null}

          {/* Data and time */}
          {item?.is_member === 1 ? (
            item?.message_content === "" ? null : item?.createdAt ? (
              <Text style={styles.timeText}>
                {(() => {
                  // Parse the ISO date string
                  const inputDate = dayjs(item?.createdAt);

                  // Current date
                  const today = dayjs();

                  // Calculate the difference in days
                  const diffInDays = today.diff(inputDate, "day");

                  // Format based on time difference
                  if (diffInDays === 0) {
                    // If the input date is today, display only the time
                    return inputDate.format("HH:mm");
                  } else if (diffInDays === 1) {
                    // If the input date is yesterday, display 'Yesterday'
                    return "Yesterday";
                  } else {
                    // For other dates, display in the format 'Month Day, Year'
                    return inputDate.format("MMM D, YYYY");
                  }
                })()}
              </Text>
            ) : null
          ) : (
            <TouchableOpacity
              style={{
                paddingVertical: 5,
                paddingHorizontal: 10,
                borderWidth: 1,
                borderRadius: 3,
                borderColor:
                  item?.is_request_for_group === 1
                    ? BaseColors.green
                    : BaseColors.activeTab,
                position: "absolute",
                right: 0,
                top: 0,
              }}
              onPress={() => {
                if (
                  isEmpty(isCurrentPlan) &&
                  !activePlanData?.is_prime_user &&
                  !activePlanData?.is_free_user
                ) {
                  setIsPaymentModal(true);
                } else {
                  setRequestId(item?.group_details_id);
                  handleRequestToJoin(item?.group_details_id);
                }
              }}
              disabled={item?.is_request_for_group === 1 ? true : false}
            >
              {addLoading && requestId === item?.group_details_id ? (
                <ActivityIndicator
                  animating
                  color={BaseColors.activeTab}
                  size="small"
                />
              ) : (
                <Text
                  style={{
                    fontSize: 14,
                    fontFamily: FontFamily.RobotoMedium,
                    color:
                      item?.is_request_for_group === 1
                        ? BaseColors.green
                        : BaseColors.activeTab,
                  }}
                >
                  {item?.is_request_for_group === 1 ? "Requested" : "Add"}
                </Text>
              )}
            </TouchableOpacity>
          )}

          {/* message and tick */}

          {!isEmpty(searchBtnData) ? null : (
            <View style={styles.tickAndMessage}>
              {item?.is_my_message ? (
                <View style={{ flexDirection: "row" }}>
                  {(!isEmpty(typingData?.text) &&
                    (typingData?.group_details_id === item?.group_details_id ||
                      typingData?.user_id === item?.userData?.user_id)) ||
                  item?.is_read === "" ? null : (
                    <CustomIcon
                      name={
                        item?.is_read === "1" || item?.is_read === "0"
                          ? "two-tic"
                          : item?.is_read === "1" || item?.is_read === "0"
                            ? "two-tic"
                            : "tick"
                      }
                      size={
                        item?.is_read === "1" || item?.is_read === "0" ? 16 : 11
                      }
                      color={
                        item?.is_read === "1"
                          ? BaseColors.blueTik
                          : BaseColors.gray6
                      }
                    />
                  )}
                </View>
              ) : null}
              {!isEmpty(typingData?.text) &&
              (typingData?.group_details_id === item?.group_details_id ||
                typingData?.user_id === item?.userData?.user_id) ? (
                <Text style={styles.typingText}>Typing...</Text>
              ) : item?.type === "group" || item?.type === "reply" ? (
                <Text style={styles.messageText}>
                  {truncate(item?.lastMessage || "", {
                    length: 30,
                    omission: "...",
                  })}
                </Text>
              ) : item?.type === "audio" ? (
                <Text style={styles.messageText}>Audio</Text>
              ) : item?.type === "post" ? (
                <Text style={styles.messageText}>Post</Text>
              ) : item?.type === "sticker" ? (
                <Text style={styles.messageText}>Sticker</Text>
              ) : item?.type === "reel" ? (
                <Text style={styles.messageText}>Reel</Text>
              ) : item?.type === "story" ? (
                <Text style={styles.messageText}>Story</Text>
              ) : item?.type === "profile" ? (
                <Text style={styles.messageText}>Profile</Text>
              ) : item?.type === "story_reply" ? (
                <Text style={styles.messageText}>Story</Text>
              ) : null}
            </View>
          )}

          {/* message Count */}
          {item?.is_member === 1 && Number(item?.message_count) > 0 ? (
            <View style={styles.messageCount}>
              <Text style={styles.messageCountText}>
                {Number(item?.message_count) > 99
                  ? "99+"
                  : item?.message_count || 0}
              </Text>
            </View>
          ) : null}
        </View>
      </TouchableOpacity>
    );
  };

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    groupChatList("refresh");
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  }, []);

  const handleScroll = (event) => {
    const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;
    const isCloseToBottom =
      layoutMeasurement.height + contentOffset.y >= contentSize.height - 100;

    if (isCloseToBottom && hasMore && !isPaginationLoading && !chatLoader) {
      const nextPage = page + 1;
      if (searchBtnData) {
        handleSearchChatList(searchBtnData, nextPage, "", "pagination");
      } else {
        groupChatList("pagination", nextPage);
      }
    }
  };

  return (
    <View style={styles.main}>
      <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding">
        <ScrollView
          onScroll={handleScroll}
          scrollEventThrottle={16}
          keyboardShouldPersistTaps="handled"
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[BaseColors.activeTab]} // Customize refresh indicator color
              tintColor={BaseColors.activeTab} // Customize refresh indicator color (Android)
            />
          }
          showsVerticalScrollIndicator={false}
        >
          {loading || chatLoader ? (
            <MiniLoader size="medium" />
          ) : (
            <View
              style={{ marginBottom: Platform.OS === "ios" ? 0 : 40, flex: 1 }}
            >
              <FlatList
                data={
                  !isEmpty(searchBtnData)
                    ? !isEmpty(searchChatList?.data)
                      ? searchChatList?.data
                      : groupChat
                    : groupChat
                }
                renderItem={(item) => {
                  return (
                    <RenderDataItem navigation={navigation} item={item?.item} />
                  );
                }}
                showsVerticalScrollIndicator={false}
                keyboardShouldPersistTaps="handled"
                ListEmptyComponent={
                  <View style={styles.centerMain}>
                    <NoRecord
                      title={"Empty"}
                      groupChat
                      type="chat"
                      onPressCreateGroup={() => {
                        if (
                          isEmpty(isCurrentPlan) &&
                          !activePlanData?.is_prime_user &&
                          !activePlanData?.is_free_user
                        ) {
                          setIsPaymentModal(true);
                        } else {
                          navigation.navigate("CreateGroupChat");
                        }
                      }}
                    />
                  </View>
                }
                ListFooterComponent={() => {
                  if (isPaginationLoading && hasMore && !isEmpty(groupChat)) {
                    return (
                      <View
                        style={{ paddingVertical: 20, alignItems: "center" }}
                      >
                        <ActivityIndicator
                          size="small"
                          color={BaseColors.activeTab}
                        />
                      </View>
                    );
                  }
                  return null;
                }}
                style={{ marginBottom: Platform.OS === "ios" ? 50 : 0 }}
              />
            </View>
          )}
        </ScrollView>
        {!isEmpty(groupChat) && isEmpty(searchChatList?.data) && (
          <TouchableOpacity
            style={styles.addUserContainer}
            activeOpacity={0.8}
            onPress={() => {
              if (
                isEmpty(isCurrentPlan) &&
                !activePlanData?.is_prime_user &&
                !activePlanData?.is_free_user
              ) {
                setIsPaymentModal(true);
              } else {
                navigation.navigate("CreateGroupChat");
              }
            }}
          >
            <View style={styles.circle}>
              <Iicons
                name="person-add-outline"
                size={30}
                color={BaseColors.activeTab}
              />
            </View>
          </TouchableOpacity>
        )}

        <PurChasePlanModal
          visible={isPaymentModal}
          setModalVisible={(e) => setIsPaymentModal(e)}
          text={"currentlyPlanText"}
          navigation={navigation}
        />
      </KeyboardAvoidingView>
    </View>
  );
};

export default GroupChat;
