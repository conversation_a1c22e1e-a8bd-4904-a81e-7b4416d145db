import React, { useCallback, useEffect, useRef, useState } from "react";
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Platform,
  ScrollView,
  KeyboardAvoidingView,
  SafeAreaView,
  Modal,
  Keyboard,
  TouchableWithoutFeedback,
  ActivityIndicator,
} from "react-native";
import styles from "./styles";
import { isEmpty, isNull } from "lodash-es";
import { BaseColors } from "@config/theme";
import CSearch from "@components/CSearch";
import { CustomIcon } from "@config/LoadIcons";
import { useFocusEffect } from "@react-navigation/native";
import Toast from "react-native-simple-toast";
import { useDispatch, useSelector } from "react-redux";
import CHeader from "@components/CHeader";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import {
  createGroup,
  getChatSearchData,
  getGroupMemberList,
  natureOfBusinessData,
} from "./apiCallFunction";
import SocketActions from "@redux/reducers/socket/actions";
import { Image } from "react-native";
import CInput from "@components/TextInput";
import CMultiDropdown from "@components/CDropDown/CMultiDropdown";
import RadioButton from "@components/CRadioButton";
import CButton from "@components/CButton";
import { groupUserList } from "@config/staticData";
import { FontFamily } from "@config/typography";
import CDropdown from "@components/CDropDown";
import { translate } from "../../lang/Translate";
import FastImage from "react-native-fast-image";
import AlreadyHaveStoryModal from "@components/AlreadyHaveStoryModal";
import { pickDocument } from "@screens/SingUpWithCompanyDetail/apiFunctions";
import CCamera from "@components/CameraButton/CCamera";
import { images } from "@config/images";
import NoRecord from "@components/NoRecord";

const { getChatList, setTotalMsgCount, setSelectedRoom } = SocketActions;

/**
 * CreateGroupChat Component
 *
 * This component handles the creation of group chats with the following features:
 * - Group profile picture selection
 * - Group name input
 * - Country selection via multi-dropdown
 * - Group type selection (Private/Public/Auto Join)
 * - Member selection and management
 * - Search functionality for finding users
 *
 * @param {Object} navigation - React Navigation object for screen navigation
 */
const CreateGroupChat = ({ navigation, route }) => {
  const dispatch = useDispatch();

  // ==================== STATE MANAGEMENT ====================

  // Modal and UI states
  const [visible, setModalVisible] = useState(false); // Controls member selection modal visibility
  const [blockScreen, setBlockScreen] = useState(false); // Prevents certain actions when screen is blocked
  const [loading, setLoading] = useState(false); // Loading state for search operations
  const [isLoading, setIsLoading] = useState(false);
  const [profilePictureModal, setProfilePictureModal] = useState(false);
  const [profileImage, setProfileImage] = useState({});
  const [isCameraOpen, setIsCameraOpen] = useState(false);
  const [imagePath, setImagePath] = useState({});

  // Search functionality states
  const [typingTimeout, setTypingTimeout] = useState(null); // Debounce timer for search input
  const [searchBtnData, setSearchBtnData] = useState(null); // Current search query
  const [searchChatList, setSearchChatList] = useState([]); // Search results for users

  // Group configuration states
  const [selectedOption, setSelectedOption] = useState({
    id: 3,
    options: "Auto Join in Group",
    key: "auto_join",
  }); // Selected group type (1=Private, 2=Public, 3=Auto Join)
  const [userList, setUserList] = useState(groupUserList); // Complete list of available users
  const [addedMembers, setAddedMembers] = useState([]); // List of users added to the group
  const [natureOfBusinessList, setNatureOfBusinessList] = useState([]);
  const [selectnob, setSelectnob] = useState([]);
  const from = route?.params?.from || "";
  const [grupName, setGrupName] = useState(route?.params?.groupName || "");
  const [hasMore, setHasMore] = useState(false);
  const [page, setPage] = useState(1);
  const [isPaginationLoading, setIsPaginationLoading] = useState(false);
  const [groupDescription, setGroupDescription] = useState("");

  // Add these state variables for errors
  const [errors, setErrors] = useState({
    profileImage: false,
    groupName: false,
    natureOfBusiness: false,
    members: false,
  });

  // ==================== REDUX STATE ====================

  // Socket-related states from Redux
  const {
    chatRooms,
    chatLoader,
    chatListNextEnablePage,
    chatData,
    typingData,
    bottomLoader,
  } = useSelector((s) => s.socket);

  // Authentication and subscription states
  const { isCurrentPlan, activePlanData, userData } = useSelector(
    (a) => a.auth
  );

  // ==================== DAYJS CONFIGURATION ====================
  dayjs.extend(utc);

  // ==================== SEARCH FUNCTIONALITY ====================

  /**
   * Handles search input with debouncing
   * Delays API call by 1 second after user stops typing
   * Only executes if user has valid subscription plan
   *
   * @param {string} searchBtnData - The search query entered by user
   */
  const handleInputChange = useCallback(
    (searchBtnData) => {
      clearTimeout(typingTimeout);
      setTypingTimeout(
        setTimeout(async () => {
          handleSearchChatList(searchBtnData, 1);
        }, 300)
      );
    },
    [typingTimeout, searchBtnData]
  );

  // ==================== FOCUS EFFECTS ====================

  const fetchNatureOfBusinessData = async () => {
    const resp = await natureOfBusinessData();
    setNatureOfBusinessList(resp);
  };

  const getUserShareList = useCallback(
    async (pageNumber = 1, type = "") => {
      // Prevent multiple concurrent API calls for pagination
      if (type === "pagination" && isPaginationLoading) {
        return;
      }

      if (type === "pagination") {
        setIsPaginationLoading(true);
      } else {
        setIsLoading(true);
      }

      const resp = await getGroupMemberList(
        userData?.user_id,
        "",
        "",
        pageNumber
      );
      if (resp?.success) {
        if (pageNumber === 1) {
          setUserList(resp?.data);
        } else {
          setUserList([...userList, ...resp.data]);
        }
        setPage(Number(resp?.pagination?.currentPage));
        if (resp?.pagination.nextPage) {
          setHasMore(true);
        } else {
          setHasMore(false);
        }
        setIsLoading(false);
        setIsPaginationLoading(false);
      } else {
        setIsLoading(false);
        setIsPaginationLoading(false);
        setUserList([]);
      }
    },
    [userData, userList, isPaginationLoading]
  );

  /**
   * Reset component state when screen comes into focus
   * Clears search data and resets message counts
   */
  useFocusEffect(
    useCallback(() => {
      setBlockScreen(false);
      setSearchBtnData(null);
      dispatch(setTotalMsgCount(0));
      dispatch(setSelectedRoom({}));
      fetchNatureOfBusinessData();
      getUserShareList();
    }, [])
  );

  useEffect(() => {
    setSearchChatList([]);
  }, [visible]);

  // ==================== SIDE EFFECTS ====================

  /**
   * Trigger search when searchBtnData changes
   * Only if user has valid subscription
   */
  useEffect(() => {
    if (
      !isEmpty(isCurrentPlan) ||
      activePlanData?.is_prime_user ||
      activePlanData?.is_free_user
    ) {
      if (!isNull(searchBtnData)) {
        handleInputChange(searchBtnData);
      }
    }
  }, [searchBtnData]);

  // ==================== API FUNCTIONS ====================

  /**
   * Handles user search API call
   *
   * @param {string} searchBtnData - Search query (trimmed)
   * @param {number} page - Page number for pagination (default: 1)
   */
  const handleSearchChatList = async (
    searchBtnData,
    pageNumber = 1,
    type = ""
  ) => {
    console.log("🚀 ~ handleSearchChatList ~ type:", type);
    if (!searchBtnData || !searchBtnData.trim()) {
      setSearchChatList([]);
      setIsLoading(false);
      setIsPaginationLoading(false);
      getUserShareList(1); // Reload original user list
      return;
    }

    if (selectedOption?.key === "private") {
      // Prevent multiple concurrent API calls for pagination
      if (type === "pagination" && isPaginationLoading) {
        return;
      }

      if (type === "pagination") {
        setIsPaginationLoading(true);
      } else {
        setIsLoading(true);
      }

      const resp = await getGroupMemberList(
        userData?.user_id,
        searchBtnData?.trim(),
        "",
        pageNumber
      );
      if (resp?.success) {
        if (pageNumber === 1) {
          setUserList(resp.data);
        } else {
          setUserList([...userList, ...resp.data]);
        }
        setPage(Number(resp?.pagination?.currentPage));
        if (resp?.pagination.nextPage) {
          setHasMore(true);
        } else {
          setHasMore(false);
        }
        setIsLoading(false);
        setIsPaginationLoading(false);
      } else {
        setUserList([]);
        setIsLoading(false);
        setIsPaginationLoading(false);
      }
    } else {
      if (type === "pagination") {
        setIsPaginationLoading(true);
      } else {
        setIsLoading(true);
      }

      const resp = await getChatSearchData(searchBtnData?.trim(), pageNumber);
      if (resp?.data?.success && !isEmpty(resp?.data?.data)) {
        const updatedData = resp?.data?.data.map((item) => ({
          ...item,
          status:
            addedMembers &&
            addedMembers.some((member) => member.user_id === item.user_id)
              ? "Remove"
              : "Add",
        }));
        setPage(Number(resp?.data?.currentPage));
        if (pageNumber === 1) {
          setSearchChatList(updatedData);
        } else {
          setSearchChatList([...searchChatList, ...updatedData]);
        }
        if (resp?.data?.hasNextPage) {
          setHasMore(true);
        } else {
          setHasMore(false);
        }
        setIsLoading(false);
        setIsPaginationLoading(false);
      } else {
        setIsLoading(false);
        setIsPaginationLoading(false);
        setSearchChatList([]);
      }
    }
  };

  // ==================== STATIC DATA ====================

  /**
   * Group type options
   */
  const groupType = [
    {
      id: 1,
      options: "Private Group",
      key: "private",
    },
    {
      id: 2,
      options: "Public Group",
      key: "public",
    },
    {
      id: 3,
      options: "Auto Join in Group",
      key: "auto_join",
    },
  ];

  // ==================== MEMBER MANAGEMENT ====================

  /**
   * Toggles member status between "Add" and "Remove"
   * Updates both userList and addedMembers states
   *
   * @param {number} userId - ID of the user to toggle
   */
  const handleToggleMember = (userId) => {
    if (searchBtnData) {
      setSearchChatList((prevList) => {
        const updatedList = prevList.map((user) => {
          if (user.user_id === userId) {
            return {
              ...user,
              status: user.status === "Add" ? "Remove" : "Add",
            };
          }
          return user;
        });
        const toggledUser = updatedList.find((u) => u.user_id === userId);
        setAddedMembers((prev) => {
          if (toggledUser?.status === "Remove") {
            // ✅ Add user if status is "Remove"
            const merged = [...prev, toggledUser];
            const unique = merged.filter(
              (item, index, self) =>
                index === self.findIndex((t) => t.user_id === item.user_id)
            );
            return unique;
          } else {
            // ❌ Remove user if toggled back to "Add"
            return prev.filter((item) => item.user_id !== userId);
          }
        });

        return updatedList;
      });
    } else {
      setUserList((prevList) => {
        const updatedList = prevList.map((user) =>
          user.user_id === userId
            ? { ...user, status: user.status === "Add" ? "Remove" : "Add" }
            : user
        );
        setAddedMembers((prev) => {
          if (
            updatedList.find((u) => u.user_id === userId)?.status === "Remove"
          ) {
            // ✅ Add user if status is "Remove"
            const merged = [
              ...prev,
              updatedList.find((u) => u.user_id === userId),
            ];
            const unique = merged.filter(
              (item, index, self) =>
                index === self.findIndex((t) => t.user_id === item.user_id)
            );
            return unique;
          } else {
            // ❌ Remove user if toggled back to "Add"
            return prev.filter((item) => item.user_id !== userId);
          }
        });

        return updatedList;
      });
    }
  };

  /**
   * Removes a member from the group (called from horizontal list X button)
   *
   * @param {Object} item - User object to remove
   */
  const handleRemoveMember = (item) => {
    handleToggleMember(item.user_id);
  };

  // ==================== VALIDATION FUNCTION ====================

  const validateGroupData = () => {
    let isValid = true;
    const newErrors = {
      profileImage: false,
      groupName: false,
      natureOfBusiness: false,
      members: false,
    };

    if (isEmpty(profileImage)) {
      newErrors.profileImage = true;
      isValid = false;
    }

    if (isEmpty(grupName?.trim())) {
      newErrors.groupName = true;
      isValid = false;
    } else if (grupName.length < 2) {
      newErrors.groupName = true;
      isValid = false;
    }

    if (isEmpty(selectnob)) {
      newErrors.natureOfBusiness = true;
      isValid = false;
    }

    if (isEmpty(addedMembers)) {
      newErrors.members = true;
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  // ==================== RENDER FUNCTIONS ====================

  /**
   * Renders individual member card in horizontal FlatList
   * Shows user image, name, and remove button
   *
   * @param {Object} item - User object
   * @param {number} index - Array index
   */
  const renderItem = ({ item, index }) => {
    return (
      <View style={{ marginHorizontal: 5 }}>
        {/* User Profile Image */}
        {item.profile_picture || item.user_dp ? (
          <FastImage
            source={{ uri: item.profile_picture || item.user_dp }}
            style={styles.avatar}
          />
        ) : (
          <FastImage
            source={
              userData?.gender === "male" ? images.manAvatar : images.manAvatar
            }
            style={styles.avatar}
          />
        )}
        {/* <Image
          source={{ uri: item.user_dp }}
          style={{ width: 60, height: 60, borderRadius: 25 }}
        /> */}

        {/* User Name */}
        <Text
          style={{
            fontSize: 14,
            fontFamily: FontFamily.RobotoRegular,
            color: BaseColors.black,
            width: 55,
          }}
          numberOfLines={1}
        >
          {item.full_name}
        </Text>

        {/* Remove Button */}
        <TouchableOpacity
          style={styles.removeBtn}
          activeOpacity={0.8}
          onPress={() => handleRemoveMember(item)}
        >
          <CustomIcon name="BsX" size={15} color={"#9DB2CE"} />
        </TouchableOpacity>
      </View>
    );
  };

  const HandleSelectImage = async (type) => {
    const data = await pickDocument(type, imagePath);
    setProfilePictureModal(false);
    // setIsLoading(false);
    setProfileImage(data);
    // setValue("companyProfile", data);
  };

  useEffect(() => {
    if (isCameraOpen) {
      setProfilePictureModal(false);
    }
    if (!isCameraOpen && imagePath?.uri) {
      setProfilePictureModal(false);
      HandleSelectImage("captureImg");
    }
  }, [isCameraOpen]);

  const renderUserList = ({ item, index }) => {
    return (
      <View
        key={item.id}
        style={{
          flexDirection: "row",
          alignItems: "center",
          marginBottom: 10,
        }}
      >
        {/* User Profile Image */}
        {item.profile_picture || item.user_dp ? (
          <FastImage
            source={{ uri: item.profile_picture || item.user_dp }}
            style={styles.avatar}
          />
        ) : (
          <FastImage
            source={
              userData?.gender === "male" ? images.manAvatar : images.manAvatar
            }
            style={styles.avatar}
          />
        )}

        {/* User Info */}
        <View style={{ marginLeft: 10 }}>
          <Text
            style={{
              fontSize: 18,
              fontFamily: FontFamily.RobotoMedium,
              marginBottom: 5,
              color: BaseColors.black,
            }}
          >
            {item.full_name}
          </Text>
        </View>

        {/* Add/Remove Button */}
        <View
          style={{
            flex: 1,
            alignItems: "flex-end",
          }}
        >
          <TouchableOpacity
            style={{
              paddingHorizontal: 10,
              paddingVertical: 6,
              borderWidth: 1,
              borderRadius: 5,
              borderColor: BaseColors.activeTab,
              backgroundColor:
                item.status === "Remove"
                  ? BaseColors.white
                  : BaseColors.activeTab,
            }}
            onPress={() => handleToggleMember(item.user_id)}
            activeOpacity={0.8}
          >
            <Text
              style={{
                color:
                  item.status === "Remove"
                    ? BaseColors.activeTab
                    : BaseColors.white,
              }}
            >
              {item.status}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const createGrop = async () => {
    setLoading(true);
    // First validate all required fields
    if (!validateGroupData()) {
      setLoading(false);
      return;
    }

    const member_ids = addedMembers.map((item) => item.user_id);
    const data = {
      group_name: grupName || "",
      type: selectedOption?.key || "private",
      nature_business_id: JSON.stringify(selectnob),
      member: JSON.stringify(member_ids),
      image: profileImage,
      description: groupDescription || "",
    };

    // Call API function only if validation passes
    const resp = await createGroup(data);
    if (resp?.success) {
      setLoading(false);
      Toast.show(resp?.message || "Group created successfully");
      if (from === "createPostScreen") {
        navigation.navigate("Chat", {
          screen: "chatTab",
          params: {},
        });
      } else {
        navigation.goBack();
      }
    } else {
      setLoading(false);
    }
  };
  const handleLoadMore = (event) => {
    const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;
    const isCloseToBottom =
      layoutMeasurement.height + contentOffset.y >= contentSize.height - 100;

    if (isCloseToBottom && hasMore && !isPaginationLoading && !isLoading) {
      const nextPage = page + 1;
      if (searchBtnData) {
        handleSearchChatList(searchBtnData, nextPage, "pagination");
      } else {
        getUserShareList(nextPage, "pagination");
      }
    }
  };

  // ==================== MAIN RENDER ====================
  return (
    <SafeAreaView style={styles.main} onPress={() => Keyboard.dismiss()}>
      {isCameraOpen ? (
        <View style={{ flex: 1 }}>
          <CCamera
            setImagePath={setImagePath}
            isCameraOpen={isCameraOpen}
            setIsCameraOpen={setIsCameraOpen}
            recordVideo={false}
          />
        </View>
      ) : (
        <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
          <View style={{ flex: 1 }}>
            <KeyboardAvoidingView
              style={{ flex: 1 }}
              behavior={Platform.OS === "ios" ? "padding" : undefined}
            >
              {/* Header */}
              <CHeader
                headingTitle={"Create Groups"}
                handleBackButton={() => navigation.goBack()}
              />
              <ScrollView
                bounces={false}
                contentContainerStyle={{ flexGrow: 1 }}
              >
                {/* Group Profile Picture Section */}
                <TouchableOpacity
                  style={styles.profileImgMainViewStyle}
                  activeOpacity={0.8}
                  onPress={() => setProfilePictureModal(true)}
                >
                  {isEmpty(profileImage) ? (
                    <View
                      style={[
                        styles.profileImgMainView,
                        { backgroundColor: "#C4C4C4" },
                      ]}
                    >
                      <CustomIcon name="Camera" size={35} color={"#8E8383"} />
                    </View>
                  ) : (
                    <View style={styles.profileImgMainView}>
                      <FastImage
                        source={{
                          uri:
                            Platform.OS === "ios"
                              ? `file://${profileImage?.uri?.replace("file://", "")}`
                              : profileImage?.uri,
                          priority: FastImage.priority.normal,
                        }}
                        style={styles.profileImgStyle}
                        resizeMode={FastImage.resizeMode.cover}
                      />
                    </View>
                  )}
                  <TouchableOpacity
                    style={[styles.editMainViewStyle]}
                    activeOpacity={0.9}
                    onPress={() => setProfilePictureModal(true)}
                  >
                    <CustomIcon
                      name={"Edit-Square"}
                      size={25}
                      color={BaseColors.activeTab}
                    />
                  </TouchableOpacity>
                </TouchableOpacity>
                {errors.profileImage && (
                  <Text style={{ color: "red", marginLeft: 20 }}>
                    Please select a group profile picture
                  </Text>
                )}

                {/* Group Name Input */}
                <View style={styles.moreAboutTextInputStyle}>
                  <CInput
                    placeholderText={"Type Group Name (e.g. #Nike)"}
                    containerStyle={styles.textInputStyle}
                    maxLength={20}
                    onChange={(text) => {
                      let cleanText = text;
                      // Allow empty input
                      if (cleanText === "") {
                        setGrupName("");
                        return;
                      }

                      // 1. Ensure only one # and only at the start
                      if (!cleanText.startsWith("#")) {
                        cleanText = "#" + cleanText.replace(/#/g, ""); // force # at beginning
                      } else {
                        cleanText = "#" + cleanText.slice(1).replace(/#/g, ""); // remove extra #
                      }

                      // 2. Remove any special characters except #
                      cleanText = cleanText.replace(/[^a-zA-Z0-9#]/g, "");

                      // 3. Limit to 15 characters
                      cleanText = cleanText.slice(0, 20);

                      setGrupName(cleanText);
                    }}
                    value={grupName}
                    isErrorMsg={
                      errors?.groupName ? "Please enter group name" : ""
                    }
                  />
                </View>

                <View style={styles.moreAboutTextInputStyle}>
                  <CInput
                    placeholderText={"Type Description"}
                    containerStyle={styles.textInputStyle}
                    maxLength={255}
                    multiline
                    numberOfLines={3}
                    onChange={(text) => {
                      const trimmed = text.trim();
                      // Only update if not empty after trimming, or show validation message
                      if (trimmed.length > 0 || text === "") {
                        setGroupDescription(text);
                      }
                    }}
                    fontFamilyStyle={{
                      height: 80,
                    }}
                    value={groupDescription}
                  />
                </View>

                {/* Country Selection Dropdown */}
                <View style={[styles.moreAboutTextInputStyle]}>
                  <CMultiDropdown
                    showText="showTextshowText"
                    nature_of_business
                    data={natureOfBusinessList}
                    required
                    multiple
                    search={false}
                    placeholder={translate("Nature of business")}
                    value={selectnob || []}
                    valueProp="nature_of_business_id"
                    listProps="label"
                    setItem={(e) => {
                      // onChange(e);
                      setSelectnob(e);
                    }}
                    listPosition="top"
                    setValue={setSelectnob}
                    // dropDownName={"SelectedCountry"}
                    showError={errors?.natureOfBusiness}
                    errorMsg={"Please select nature of business"}
                    setCountryIds={setSelectnob}
                  />
                </View>

                {/* Group Type Selection */}
                <View style={[styles.moreAboutTextInputStyle]}>
                  <RadioButton
                    type="options"
                    options={groupType}
                    defaultValue={selectedOption?.id}
                    selectedOption1={(e) => {
                      setSelectedOption(e);
                    }}
                  />
                </View>

                {/* Add Member Button */}
                {isEmpty(addedMembers) ? (
                  <View style={styles.moreAboutTextInputStyle}>
                    <Text
                      style={styles.addText}
                      onPress={() => {
                        setModalVisible(true);
                        getUserShareList();
                      }}
                    >
                      Add Member
                    </Text>
                  </View>
                ) : (
                  <TouchableOpacity
                    style={{
                      borderWidth: 0.6,
                      borderRadius: 5,
                      borderColor: "#8E8383",
                      paddingHorizontal: 10,
                      paddingVertical: 15,
                      marginBottom: 20,
                      marginHorizontal: 20,
                    }}
                    onPress={() => {
                      setModalVisible(true);
                    }}
                    activeOpacity={0.8}
                  >
                    <FlatList
                      data={addedMembers}
                      renderItem={renderItem}
                      horizontal={true}
                      keyExtractor={(item, index) => item.user_id}
                      showsHorizontalScrollIndicator={false}
                    />
                  </TouchableOpacity>
                )}
                {errors.members && (
                  <Text style={{ color: "red", marginLeft: 20 }}>
                    Please add at least one member
                  </Text>
                )}
              </ScrollView>
            </KeyboardAvoidingView>

            {/* Create Group Button */}
            <CButton
              style={styles.buttonView}
              onBtnClick={() => createGrop()}
              loading={loading}
            >
              Create Group
            </CButton>

            {/* ==================== MEMBER SELECTION MODAL ==================== */}
            <Modal
              animationType="slide"
              transparent={true}
              animationInTiming={5000}
              animationOutTiming={5000}
              visible={visible}
              onRequestClose={() => {
                setModalVisible(!visible);
                setSearchBtnData(null);
              }}
            >
              <View style={styles.ovarlayStyle}>
                <View style={styles.modalView}>
                  {/* Modal Header */}
                  <View
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                      justifyContent: "space-between",
                    }}
                  >
                    <Text style={styles.modalTitleText}>{"Add Members"}</Text>

                    {/* Close Button */}
                    <TouchableOpacity
                      style={{
                        borderWidth: 1,
                        height: 24,
                        width: 24,
                        borderRadius: 5,
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                      activeOpacity={0.8}
                      onPress={() => {
                        setModalVisible(false);
                        setSearchBtnData(null);
                      }}
                    >
                      <CustomIcon
                        name="BsX"
                        size={20}
                        color={BaseColors.black}
                      />
                    </TouchableOpacity>
                  </View>

                  {/* Search Component */}
                  <View
                    style={[
                      styles.searchView,
                      { marginHorizontal: 0, marginBottom: 20 },
                    ]}
                  >
                    <CSearch
                      searchBtnData={searchBtnData}
                      setSearchBtnData={setSearchBtnData}
                    />
                  </View>

                  {/* Selected Members Horizontal List */}
                  {isEmpty(addedMembers) ? null : (
                    <View
                      style={{
                        borderWidth: 0.6,
                        borderRadius: 5,
                        borderColor: "#8E8383",
                        paddingHorizontal: 10,
                        paddingVertical: 15,
                        marginBottom: 20,
                      }}
                    >
                      <FlatList
                        data={addedMembers}
                        renderItem={renderItem}
                        horizontal={true}
                        keyExtractor={(item, index) => item.user_id}
                        showsHorizontalScrollIndicator={false}
                      />
                    </View>
                  )}

                  {/* Available Users Vertical List */}
                  <ScrollView
                    onScroll={handleLoadMore}
                    scrollEventThrottle={16}
                    keyboardShouldPersistTaps="handled"
                    showsVerticalScrollIndicator={false}
                    nestedScrollEnabled
                  >
                    <FlatList
                      data={
                        !isEmpty(searchBtnData)
                          ? !isEmpty(searchChatList) && !isNull(searchBtnData)
                            ? searchChatList
                            : userList
                          : userList
                      }
                      renderItem={renderUserList}
                      showsVerticalScrollIndicator={false}
                      ListEmptyComponent={
                        <View style={styles.centerMain}>
                          <NoRecord title="noRecordFound" />
                        </View>
                      }
                      ListFooterComponent={() => {
                        if (isPaginationLoading && hasMore) {
                          return (
                            <View
                              style={{
                                paddingVertical: 20,
                                alignItems: "center",
                              }}
                            >
                              <ActivityIndicator
                                size="small"
                                color={BaseColors.activeTab}
                              />
                            </View>
                          );
                        }
                        return null;
                      }}
                      style={{ flex: 1 }}
                      keyExtractor={(item, index) => `${item.user_id}-${index}`}
                      nestedScrollEnabled
                    />
                  </ScrollView>
                </View>
              </View>
            </Modal>

            {profilePictureModal ? (
              <AlreadyHaveStoryModal
                visible={profilePictureModal}
                setModalVisible={(e) => setProfilePictureModal(e)}
                title1="captureFromCamera"
                title2="chooseFromGallery"
                onPressTitle1={() => setIsCameraOpen(true)}
                onPressTitle2={() => HandleSelectImage()}
              />
            ) : null}
          </View>
        </TouchableWithoutFeedback>
      )}
    </SafeAreaView>
  );
};

export default CreateGroupChat;
