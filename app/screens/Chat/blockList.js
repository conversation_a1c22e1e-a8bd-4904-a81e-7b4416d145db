import React, { useCallback, useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  KeyboardAvoidingView,
  FlatList,
  SafeAreaView,
} from "react-native";
import styles from "./styles";
import { isEmpty, truncate } from "lodash-es";
import { BaseColors } from "@config/theme";
import CHeader from "@components/CHeader";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import { blockUnBlockUser, getBlockList } from "./apiCallFunction";
import { useFocusEffect } from "@react-navigation/native";
import Toast from "react-native-simple-toast";
import FastImage from "react-native-fast-image";
import NoRecord from "@components/NoRecord";

/**
 * ChatTab Component
 *
 * Main chat interface component that provides a tabbed view for different chat types.
 * Features include:
 * - Tab navigation between Personal chats and Group chats
 * - Search functionality for finding chats
 * - Block list management
 * - User subscription validation for premium features
 * - Real-time chat updates via socket connection
 *
 * @param {Object} navigation - React Navigation object for screen navigation
 */
const BlockList = ({ navigation }) => {
  // ==================== STATE MANAGEMENT ====================

  // Modal and UI states
  const [loading, setLoading] = useState(false); // Loading state for API operations
  // Block list management states
  const [blockUserList, setBlockUserList] = useState([]); // List of blocked users

  // ==================== DAYJS CONFIGURATION ====================
  dayjs.extend(utc); // Extend dayjs with UTC plugin for timezone handling

  // ==================== BLOCK LIST MANAGEMENT ====================

  /**
   * Fetches the list of blocked users from the server
   * Handles pagination and updates the blockUserList state
   *
   * @param {number} page - Page number for pagination (default: 1)
   */
  const getBlockListData = async (page = 1) => {
    setLoading(true);

    try {
      const resp = await getBlockList();

      if (resp?.data?.success && !isEmpty(resp?.data?.data)) {
        // Update block list with pagination support
        setBlockUserList({
          page: page,
          next_enable: resp?.data?.hasNextPage,
          data:
            page > 1
              ? [...blockUserList?.data, ...resp?.data?.data] // Append for pagination
              : resp?.data?.data, // Replace for new fetch
        });
      } else {
        // Clear block list if no data found
        setBlockUserList([]);
      }
    } catch (error) {
      console.error("Error fetching block list:", error);
      setBlockUserList([]);
    } finally {
      setLoading(false);
    }
  };

  // ==================== OPTIONS MENU HANDLER ====================

  useFocusEffect(
    useCallback(() => {
      getBlockListData();
    }, [])
  );

  const handleUnblock = async (user_id) => {
    const resp = await blockUnBlockUser(user_id);
    if (resp !== undefined && resp?.data?.success) {
      const filteredData = blockUserList?.data?.filter(
        (item) => item?.userData?.user_id !== user_id
      );
      setBlockUserList({
        ...blockUserList,
        data: filteredData,
      });
      Toast.show(resp?.data?.message || "User unblocked !");
    } else {
      Toast.show(
        resp?.data?.message || "Something went wrong please try again"
      );
    }
  };

  const RenderDataItem = ({ item, navigation }) => {
    return (
      <TouchableOpacity style={styles.renderItemMainView} activeOpacity={1}>
        {/* Image */}
        <View>
          <FastImage
            source={{ uri: item?.userData?.user_dp }}
            style={styles.imgView}
          />

          {/* green tick */}
          {item?.isOnline ? <View style={styles.greenDot}></View> : null}
        </View>

        {/* User Titles */}
        <View style={styles.titleView}>
          {item?.userData?.username ? (
            <Text style={styles.userNameViewText}>
              {truncate(item?.userData?.full_name, {
                length: 23,
                omission: "...",
              })}
            </Text>
          ) : null}

          {/* Data and time */}
          {item?.message_content === "" ? null : item?.createdAt ? (
            <Text style={styles.timeText}>{item?.createdAt}</Text>
          ) : null}

          {/*  Unblock View*/}

          <View style={styles.unblockBtn}>
            <TouchableOpacity
              style={styles.unblockBtnView}
              activeOpacity={0.8}
              onPress={() => handleUnblock(item?.userData?.user_id)}
            >
              <Text style={styles.unblockBtnText}>Unblock</Text>
            </TouchableOpacity>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  // ==================== MAIN RENDER ====================
  return (
    <SafeAreaView style={styles.main}>
      <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding">
        {/* ==================== HEADER SECTION ==================== */}
        <CHeader
          handleBackButton={() => navigation.goBack()}
          headingTitle="blocklist"
        />

        <FlatList
          data={blockUserList?.data}
          renderItem={(item) => {
            return <RenderDataItem navigation={navigation} item={item?.item} />;
          }}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
          ListEmptyComponent={
            <View style={styles.centerMain}>
              <NoRecord title={"Empty"} type="chat" description="noChat" />
            </View>
          }
          style={{ marginBottom: Platform.OS === "ios" ? 50 : 0 }}
        />
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default BlockList;
