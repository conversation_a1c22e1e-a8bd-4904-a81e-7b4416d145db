import { getApiData } from "@app/utils/apiHelper";
import BaseSetting from "@config/setting";
import { isEmpty } from "lodash-es";
import Toast from "react-native-simple-toast";
import SocketActions from "@redux/reducers/socket/actions";

// Search Chat List
export const getChatSearchData = async (searchBtnData, page, groupId = "") => {
  if (!isEmpty(searchBtnData?.trim())) {
    try {
      const resp = await getApiData(
        `${BaseSetting.endpoints.searchChatList}?page=${page}&pageSize=10&username=${searchBtnData}&group_details_id=${groupId}`,
        "GET"
      );

      if (resp !== undefined) {
        return resp;
      } else {
        Toast.show(resp?.data?.message || "No Data Found");
      }
    } catch (error) {
      console.error("🚀 ~ getData ~ error:", error);
    }
  } else {
    return undefined;
  }
};

// Fetch Saved Reel Data
export const getBlockList = async () => {
  try {
    const resp = await getApiData(`${BaseSetting.endpoints.blockList}`, "GET");

    if (resp !== undefined) {
      return resp;
    } else {
      Toast.show(resp?.data?.message || "No Data Found");
    }
  } catch (error) {
    console.error("🚀 ~ getData ~ error:", error);
  }
};

// Block User Chat
export const blockUnBlockUser = async (receiver_id) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.blockUnblock}${receiver_id}`,
      "POST"
    );
    if (resp !== undefined) {
      return resp;
    } else {
      Toast.show(
        resp?.data?.message || "Something went wrong please try again"
      );
    }
  } catch (error) {
    console.error("🚀 ~ likeDislikeToggle ~ error:", error);
  }
};

// clear chat Data
export const clearChat = async (receiver_id) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.clearChatList}${receiver_id}`,
      "POST"
    );

    if (resp !== undefined) {
      return resp;
    } else {
      Toast.show(
        resp?.data?.message || "Something went wrong please try again"
      );
    }
  } catch (error) {
    console.error("🚀 ~ likeDislikeToggle ~ error:", error);
  }
};

// Get Audience Data Form DB
export const natureOfBusinessData = async () => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.commonData}?slug=nature_of_business`,
      "GET"
    );

    if (resp !== undefined) {
      if (resp?.data?.success) {
        return resp?.data?.data;
      } else {
        Toast.show(
          resp?.data?.message || "Something went wrong please try again"
        );
      }
    }
  } catch (error) {
    console.error("🚀 ~ natureOfBusinessData ~ error:", error);
  }
};

// Get Audience Data Form DB
export const getGroupChatList = async (dispatch, page) => {
  const { setGroupChatList } = SocketActions;
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.getGroupChat}?page=${page}&pageSize=10`,
      "GET"
    );
    if (resp !== undefined) {
      if (resp?.data?.success) {
        dispatch(setGroupChatList(resp?.data?.data));
        return resp?.data;
      } else {
        Toast.show(
          resp?.data?.message || "Something went wrong please try again"
        );
      }
    }
  } catch (error) {
    console.error("🚀 ~ natureOfBusinessData ~ error:", error);
  }
};

// Get Audience Data Form DB
export const createGroup = async (data) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.createGroup}`,
      "POST",
      data,
      {},
      true
    );
    if (resp !== undefined) {
      if (resp?.data?.success) {
        return resp?.data;
      } else {
        if (resp?.data?.message === "Network error") {
          createGroup();
        } else {
          Toast.show(
            resp?.data?.message || "Something went wrong please try again"
          );
        }
      }
    }
  } catch (error) {
    console.error("🚀 ~ natureOfBusinessData ~ error:", error);
  }
};

// Search Chat List
export const groupSearchMessage = async (searchBtnData, page) => {
  if (!isEmpty(searchBtnData?.trim())) {
    try {
      const resp = await getApiData(
        `${BaseSetting.endpoints.groupSearch}?page=${page}&pageSize=10&search=${encodeURIComponent(searchBtnData)}`,
        "GET"
      );
      if (resp !== undefined) {
        return resp;
      } else {
        Toast.show(resp?.data?.message || "No Data Found");
      }
    } catch (error) {
      console.error("🚀 ~ getData ~ error:", error);
    }
  } else {
    return undefined;
  }
};
export const groupRequestList = async (group_details_id) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.grouprequettoJoin}`,
      "POST",
      { group_details_id: group_details_id }
    );
    if (resp !== undefined) {
      return resp?.data;
    } else {
      Toast.show(resp?.data?.message || "No Data Found");
    }
  } catch (error) {
    console.error("🚀 ~ getData ~ error:", error);
  }
};

export const onRequestAction = async (group_request_id, action) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.requestAction}`,
      "POST",
      { group_request_id: group_request_id, action: action }
    );
    if (resp !== undefined) {
      return resp?.data;
    } else {
      Toast.show(resp?.data?.message || "No Data Found");
    }
  } catch (error) {
    console.error("🚀 ~ getData ~ error:", error);
  }
};

export const getGroupMemberList = async (
  id,
  search = "",
  groupId = "",
  page = 1
) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.memberList}/${id}?search=${search}&group_details_id=${groupId}&page=${page}`,
      "GET"
    );
    return resp?.data;
  } catch (error) {
    console.log("🚀 ~ getUserFollowingList ~ error:", error);
  }
};
