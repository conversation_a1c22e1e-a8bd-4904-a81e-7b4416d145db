import React, { useCallback, useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  KeyboardAvoidingView,
} from "react-native";
import styles from "./styles";
import { BaseColors } from "@config/theme";
import CSearch from "@components/CSearch";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import ChatScreen from "./index";
import GroupChat from "./groupChat";
import { useFocusEffect } from "@react-navigation/native";

/**
 * ChatTab Component
 *
 * Main chat interface component that provides a tabbed view for different chat types.
 * Features include:
 * - Tab navigation between Personal chats and Group chats
 * - Search functionality for finding chats
 * - Block list management
 * - User subscription validation for premium features
 * - Real-time chat updates via socket connection
 *
 * @param {Object} navigation - React Navigation object for screen navigation
 */
const ChatTab = ({ navigation, route }) => {
  // ==================== STATE MANAGEMENT ====================
  // Search functionality states
  const [searchBtnData, setSearchBtnData] = useState(
    route?.params?.search || null
  ); // Current search query
  // Tab navigation state
  const [currentIndex, setCurrentIndex] = useState(0); // Currently selected tab (0=Personal, 1=Groups)

  // ==================== DAYJS CONFIGURATION ====================
  dayjs.extend(utc); // Extend dayjs with UTC plugin for timezone handling

  // ==================== STATIC DATA ====================

  /**
   * Tab configuration data
   * Defines the available chat tabs and their properties
   */
  const data = [
    {
      id: 1,
      title: "Personal",
      screen: "chat",
    },
    {
      id: 2,
      title: "Groups",
      screen: "ProfileNew1",
    },
  ];

  // ==================== OPTIONS MENU HANDLER ====================

  /**
   * Handles option button clicks from the search component
   * Currently supports showing the block list
   *
   * @param {string} selectedPopOverValue - The selected option key
   */
  const handleOptionsBtn = (selectedPopOverValue) => {
    if (selectedPopOverValue === "block") {
      navigation.navigate("BlockList");
    }
  };

  useFocusEffect(
    useCallback(() => {
      if (route?.params?.search) {
        setSearchBtnData(route?.params?.search);
        setCurrentIndex(1);
      } else {
        setSearchBtnData(null);
      }
    }, [route])
  );

  // ==================== MAIN RENDER ====================
  return (
    <View style={styles.main}>
      <KeyboardAvoidingView style={{ flex: 1 }} behavior="padding">
        {/* ==================== HEADER SECTION ==================== */}
        <View style={styles.searchView}>
          <CSearch
            options // Enable options menu
            optionData={[
              {
                key: "block",
                icon: "material-symbols_block",
                text: "Block List",
              },
            ]}
            handleOptionsBtn={handleOptionsBtn}
            searchBtnData={searchBtnData}
            setSearchBtnData={setSearchBtnData}
          />
        </View>

        {/* ==================== TAB BAR SECTION ==================== */}
        <View style={styles.tabBarMainView}>
          {data.map((item, index) => {
            return (
              <TouchableOpacity
                activeOpacity={0.8}
                key={index}
                style={[
                  styles.tabBarView,
                  {
                    // Dynamic styling based on selected tab
                    backgroundColor:
                      currentIndex === index
                        ? BaseColors.activeTab // Active tab background
                        : BaseColors.textinputBackGroundColor, // Inactive tab background
                    borderWidth: 0,
                    borderColor: currentIndex === index && BaseColors.activeTab,
                  },
                ]}
                onPress={() => {
                  setSearchBtnData(null);
                  setCurrentIndex(index); // Switch to selected tab
                }}
              >
                <Text
                  style={[
                    styles.tabBarTextStyle,
                    {
                      // Dynamic text color based on selected tab
                      color:
                        currentIndex === index
                          ? BaseColors.white // Active tab text color
                          : BaseColors.black102, // Inactive tab text color
                    },
                  ]}
                >
                  {item?.title}
                </Text>
              </TouchableOpacity>
            );
          })}
        </View>

        {/* ==================== CONTENT SECTION ==================== */}
        {/* Conditionally render different chat screens based on selected tab */}
        {currentIndex === 0 ? (
          // Personal Chat Screen - Shows individual conversations
          <ChatScreen navigation={navigation} search={searchBtnData} />
        ) : (
          // Group Chat Screen - Shows group conversations
          <GroupChat navigation={navigation} search={searchBtnData} />
        )}
      </KeyboardAvoidingView>
    </View>
  );
};

export default ChatTab;
