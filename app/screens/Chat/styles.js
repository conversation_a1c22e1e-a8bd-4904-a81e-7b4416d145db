import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";

const { StyleSheet, Dimensions } = require("react-native");

const HEIGHT = Dimensions.get("window").height;

const styles = StyleSheet.create({
  main: {
    flex: 1,
    backgroundColor: BaseColors.white,
    marginBottom: 15,
  },
  sendBtnMain: {
    backgroundColor: BaseColors.activeTab,
    height: 45,
    width: 45,
    borderRadius: 25,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 10,
  },
  unblockBtnView: {
    paddingHorizontal: 14,
    paddingVertical: 5,
    borderRadius: 5,
    backgroundColor: BaseColors.white,
    borderWidth: 0.5,
    elevation: 5,
    borderColor: "#D9D9D9",
  },
  unblockBtnText: {
    color: BaseColors.black,
    fontFamily: FontFamily.InterMedium,
  },
  unblockBtn: {
    position: "absolute",
    right: 0,
    alignItems: "center",
    justifyContent: "center",
  },
  centerMain: {
    flex: 1,
    marginTop: HEIGHT / 5,
  },
  tickAndMessage: {
    flexDirection: "row",
    alignItems: "center",
    gap: 3,
  },
  secondMainScreen: {
    flex: 1,
    marginHorizontal: 6,
    marginTop: 12,
  },
  searchView: {
    marginHorizontal: 20,
    marginVertical: 10,
  },
  renderItemMainView: {
    flex: 1,
    flexDirection: "row",
    padding: 10,
    alignItems: "center",
    marginHorizontal: 10,
  },
  imgView: {
    height: 55,
    width: 55,
    borderRadius: 33,
  },
  titleView: {
    flex: 1,
    marginLeft: 16,
  },
  userNameViewText: {
    fontSize: 17,
    fontFamily: FontFamily.InterSemiBold,
    color: BaseColors.black,
    textTransform: "capitalize",
    flexWrap: "wrap",
    width: "65%",
  },
  messageText: {
    fontSize: 13.5,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.gray6,
  },
  typingText: {
    fontSize: 12,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.activeTab,
  },
  timeText: {
    fontSize: 12,
    fontFamily: FontFamily.InterSemiBold,
    color: BaseColors.black,
    position: "absolute",
    right: 0,
  },
  messageCount: {
    position: "absolute",
    right: 0,
    top: 15,
    height: 24,
    width: 24,
    borderRadius: 23,
    backgroundColor: BaseColors.activeTab,
    alignItems: "center",
    justifyContent: "center",
  },
  messageCountText: {
    fontFamily: FontFamily.RobotoRegular,
    fontSize: 11.5,
    color: BaseColors.white,
  },
  emptyAddressLottieView: {
    height: 270,
  },
  greenDot: {
    position: "absolute",
    right: 0,
    height: 18,
    width: 18,
    backgroundColor: BaseColors.green,
    borderRadius: 25,
    bottom: 0,
    borderWidth: 3,
    borderColor: BaseColors.white,
  },
  tabBarView: {
    borderWidth: 1,
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 10,
    borderRadius: 5,
  },
  tabBarTextStyle: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoMedium,
    color: BaseColors.gray9,
  },
  tabBarMainView: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginHorizontal: 20,
    marginBottom: 10,
  },
  profileImgMainViewStyle: {
    width: 120,
    height: 120,
    alignItems: "center",
    justifyContent: "center",
    alignSelf: "center",
    marginBottom: 30,
  },
  profileImgMainView: {
    height: 120,
    width: 120,
    borderRadius: 60,
    backgroundColor: "#C4C4C4",
    alignItems: "center",
    justifyContent: "center",
    alignSelf: "center",
  },
  moreAboutTextInputStyle: {
    marginBottom: 20,
    marginHorizontal: 20,
  },
  editMainViewStyle: {
    position: "absolute",
    bottom: 4,
    right: 13,
  },
  addText: {
    fontSize: 20,
    fontFamily: FontFamily.RobotoMedium,
    color: BaseColors.activeTab,
    textDecorationLine: "underline",
  },
  buttonView: {
    marginHorizontal: 20,
    marginBottom: 10,
  },
  ovarlayStyle: {
    backgroundColor: "rgba(0,0,0,0.6)",
    flex: 1,
    justifyContent: "flex-end",
  },
  modalView: {
    backgroundColor: BaseColors.white,
    padding: 20,
    borderRadius: 12,
    width: "100%",
    height: "90%",
  },

  modalTitleText: {
    fontSize: 24,
    color: BaseColors.black,
    fontFamily: FontFamily.RobotoMedium,
    textAlign: "center",
    marginBottom: 15,
    textAlignVertical: "center",
  },
  removeBtn: {
    position: "absolute",
    right: 0,
    top: 0,
    backgroundColor: BaseColors.activeTab,
    borderRadius: 25,
    padding: 1,
  },
  circle: {
    width: 70,
    height: 70,
    borderRadius: 35,
    borderWidth: 1,
    borderColor: BaseColors.activeTab,
    backgroundColor: "rgba(214, 0, 46, 0.2)", // pinkish red
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOpacity: 0.15,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
  },
  addUserContainer: {
    position: "absolute",
    bottom: 50, // Adjust based on tab height
    right: 16,
    zIndex: 999,
    backgroundColor: BaseColors.white,
  },
  profileImgStyle: {
    width: "100%",
    height: "100%",
    borderRadius: 60,
  },
  avatar: { width: 50, height: 50, borderRadius: 25 },
});
export default styles;
