/* eslint-disable react-native/no-inline-styles */
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import {
  SafeAreaView,
  View,
  FlatList,
  Text,
  TouchableOpacity,
} from "react-native";
import NavigationDrawerHeader from "@components/NavigationDrawerHeader";
import StoryComponent from "@components/StoryComponent";
import { useSelector, useDispatch } from "react-redux";
import AuthActions from "@redux/reducers/auth/actions";
import { useFocusEffect } from "@react-navigation/native";
import {
  ClickedLikeButton,
  ClickedSaveButton,
  getPostList,
  getRecomondaedGroupList,
  getSettingData,
  getSpecialOffer,
  handleToSetIsSeen,
  onReelsPressFunc,
} from "./apiCallFunction";
import isEmpty from "lodash-es/isEmpty";
import PostComponent from "@components/PostComponent/PostComponent";
import NoRecord from "@components/NoRecord";
import {
  getUserFollowList,
  getUserFollowingList,
  handleFollowToggle,
  requestForPushNotificationPermission,
} from "@app/utils/commonFunction";
import styles from "./styles";
import Toast from "react-native-simple-toast";
import MiniLoader from "@components/MiniLoader";
import AlertMessage from "@components/AlertMessage";
import { getRemainingPlanData } from "@screens/Payments/apiFunction";
import { getStoryData } from "@components/StoryComponent/apiCallFunction";
import ShimmerPostComponent from "@components/PostComponent/ShimmerPostCompoent";
import ShimmerPlaceholder from "react-native-shimmer-placeholder";
import LinearGradient from "react-native-linear-gradient";
import {
  getReelsList,
  handleToInteractionData,
  setReelDataList,
} from "@screens/ViewReels/apiFunctions";
import PurChasePlanModal from "@components/PurchasePlanModal";
import dayjs from "dayjs";
import { navigationRef } from "@navigation/NavigationService";
import OfferComponent from "@components/OfferComponent";
import ProgressBar from "@components/CProgress";
import { isNull, isUndefined } from "lodash-es";
import TagButtonComponent from "@components/TagButton";

const converter = require("number-to-words");

/**
 * About Screen
 * @module About
 *
 */

const {
  setStoryCount,
  setAdminSettingData,
  setCreatedPostList,
  setUserFollowList,
  setReelsList,
  setFriendUserData,
  setMyHighlights,
  setMyIntro,
  setMyReel,
  setMyPost,
  setUserFollowingList,
  setUserStoryList,
  setFromScreenName,
  setActivePlanData,
  setNotificationActivity,
  setNotificationCount,
  setIsCurrentPlan,
  setSpecialOfferData,
  setAppReviewCount,
} = AuthActions;

export default function Home({ navigation, route }) {
  const {
    storyCount,
    createdPostList,
    isCurrentPlan,
    subscriptionPlan,
    reelsList,
    userStoryList,
    userData,
    fromScreenName,
    isBestOffer,
    activePlanData,
    notificationCount,
    adminSettingData,
    userFollowList,
    specialOfferData,
  } = useSelector((auth) => auth.auth);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [isLikeAnim, setIsLikeAnim] = useState(false);
  const [bottomLoading, setBottomLoading] = useState(false);
  const [trailTime, setTrailTime] = useState(0);
  const [selectedLikeIndex, setSelectedLikeIndex] = useState(0);
  const [isCurrentActivePlan, setCurrentActivePlan] = useState({});
  const [isCurrentFocusIndex, setIsCurrentFocusIndex] = useState(0);
  const [isBestOffer1, setIsBestOffer] = useState({});
  const [followLodging, setFollowLodging] = useState({
    loader: false,
    selectedIndex: null,
  });
  const [isCurrentScreen, setIsCurrentScreen] = useState(true);
  const [recomndaedGroupList, setRecomndaedGroupList] = useState([]);

  const [sliderValue, setSliderValue] = useState(0);
  const [isPaymentModal, setIsPaymentModal] = useState(false);
  const dispatch = useDispatch();
  const isCurrentFocusIndexMemo = useMemo(
    () => isCurrentFocusIndex,
    [isCurrentFocusIndex]
  );

  // this APIS call only one Time
  useEffect(() => {
    setLoading(true);
    handleToGetData(1);
    handleRecomndaedList();
  }, []);

  // this post list API call only when we come from create story
  useEffect(() => {
    if (fromScreenName === "createPostScreen") {
      fetchPostData();
    }
    if (fromScreenName === "createStoryScreen") {
      handleToGetData((page = 1));
    }
  }, [fromScreenName]);

  useFocusEffect(
    useCallback(() => {
      setIsCurrentScreen(true);
      return () => {
        setIsCurrentScreen(false);
      };
    }, [])
  );

  useEffect(() => {
    apiFunction();
    // getSpecialOfferData();
  }, []);

  useEffect(() => {
    fetchSettingData();
  }, [activePlanData]);

  useFocusEffect(
    useCallback(() => {
      handleToSeen();
    }, [storyCount])
  );

  const apiFunction = async () => {
    requestForPushNotificationPermission();
    const data = subscriptionPlan.filter(
      (item) => item.is_special_offer === true
    );
    setIsBestOffer(data);
    dispatch(setFriendUserData({}));
    dispatch(setMyHighlights({}));
    dispatch(setMyIntro({}));
    dispatch(setMyReel({}));
    dispatch(setMyPost({}));
  };

  const handleToSeen = async () => {
    if (
      storyCount.length !== 0 &&
      navigationRef.current.getCurrentRoute()?.name === "HomeTab"
    ) {
      const resp = await handleToSetIsSeen(storyCount);
      if (resp?.data?.success === true) {
        dispatch(setStoryCount([]));
      } else {
        console.warn("Something went wrong please try again");
      }
    }
  };

  const handleRecomndaedList = async () => {
    const resp = await getRecomondaedGroupList();
    if (resp?.success) {
      setRecomndaedGroupList(resp?.data);
    }
  };

  const getUserShareList = useCallback(async () => {
    const resp = await getUserFollowList(userData?.user_id);
    if (
      resp !== undefined &&
      resp?.data?.success &&
      resp?.data?.data &&
      JSON.stringify(userFollowList) !== JSON.stringify(resp?.data)
    ) {
      dispatch(setUserFollowList(resp?.data));
    } else {
      console.log("Something went wrong please try again");
    }
    const resp1 = await getUserFollowingList(userData?.user_id);
    if (resp1 !== undefined && resp1?.data?.success && resp1?.data?.data) {
      dispatch(setUserFollowingList(resp1?.data));
    } else {
      console.log("Something went wrong please try again");
    }
  }, [userData]);

  // Fetch admin Setting Data
  const fetchSettingData = async () => {
    const resp = await getSettingData();
    if (
      resp !== undefined &&
      JSON.stringify(resp) !== JSON.stringify(adminSettingData)
    ) {
      dispatch(setAdminSettingData(resp));
    }
    if (isEmpty(isCurrentPlan)) {
      const trailTime = resp.find(
        (obj) => obj.slug === "FREEDAYTRIALLIMITINDAYS"
      );

      setTrailTime(trailTime?.value);
    }
  };
  // Fetch Post Data
  const fetchPostData = async (page = 1, bottomLoader = false) => {
    if (
      isEmpty(createdPostList?.data) ||
      fromScreenName === "createPostScreen"
    ) {
      setLoading(true);
    }
    if (bottomLoader) {
      setBottomLoading(true);
    }
    const resp = await getPostList(setLoading, page);

    if (!isEmpty(resp?.data?.data) && resp?.data?.data !== undefined) {
      dispatch(
        setCreatedPostList({
          page: page,
          next_enable: resp?.data?.hasNextPage,
          data:
            page > 1
              ? [...createdPostList?.data, ...resp?.data?.data]
              : resp?.data?.data,
        })
      );
      dispatch(setFromScreenName(""));
      dispatch(setNotificationActivity(""));
      setLoading(false);
      setBottomLoading(false);

      if (page < 2) {
        getSpecialOfferData();
      }
    } else {
      dispatch(setCreatedPostList([]));
      setBottomLoading(false);
      setLoading(false);
      dispatch(setFromScreenName(""));
      dispatch(setNotificationActivity(""));
      if (page < 2) {
        getSpecialOfferData();
      }
    }
  };

  const handleToUpdateLike = (post_id) => {
    setIsLikeAnim(true);

    // Convert the array to a Map for efficient lookups
    const postMap = new Map(
      createdPostList?.data?.map((post) => [post.post_id || post.reel_id, post])
    );

    if (postMap.has(post_id)) {
      const updatedPost = postMap.get(post_id);
      postMap.set(post_id, {
        ...updatedPost,
        like_counts: updatedPost.like_counts + (updatedPost.is_liked ? -1 : 1),
        is_liked: !updatedPost.is_liked,
      });
    }

    // Convert Map back to an array and update state
    dispatch(
      setCreatedPostList({
        ...createdPostList,
        data: Array.from(postMap.values()),
      })
    );
  };

  // Function when we like like button
  const handleLike = async (post_id, PostType) => {
    if (
      !isEmpty(isCurrentPlan) ||
      activePlanData?.is_prime_user ||
      activePlanData?.is_free_user
    ) {
      handleToUpdateLike(post_id, PostType);
      const resp = await ClickedLikeButton(post_id, PostType);
      if (resp !== undefined) {
        if (resp?.data?.success && !isEmpty(resp?.data?.data)) {
          console.log("liked");
        } else {
          handleToUpdateLike(post_id, PostType);

          Toast.show(resp?.data?.message || "No Data found");
          // setIsPaymentModal(true);
        }
      }
    } else {
      setIsPaymentModal(true);
    }
  };

  // Handle Follow function
  const handleToFollow = async (userId, selected_index, type) => {
    if (
      !isEmpty(isCurrentPlan) ||
      activePlanData?.is_prime_user ||
      activePlanData?.is_free_user
    ) {
      setFollowLodging({
        loader: true,
        selectedIndex: selected_index,
      });
      if (type === "remove") {
        userData.followings = userData.followings - 1;
      } else {
        userData.followings = userData.followings + 1;
      }
      const resp = await handleFollowToggle(userId);
      const updatedPosts = createdPostList?.data?.map((post) => {
        if (post.user_id === userId) {
          return {
            ...post,
            is_followed: !post.is_followed,
          };
        }
        return post;
      });

      dispatch(
        setCreatedPostList({
          ...createdPostList,
          data: updatedPosts,
        })
      );
      setFollowLodging({
        loader: false,
        selectedIndex: selected_index,
      });
      if (resp?.data?.success) {
        getUserShareList();
        // getUserFollowingList();
        setFollowLodging({
          loader: false,
          selectedIndex: selected_index,
        });
      } else {
        const updatedPosts = createdPostList?.data?.map((post) => {
          if (post.user_id === userId) {
            return {
              ...post,
              is_followed: post.is_followed ? false : true,
            };
          }
          return post;
        });

        if (type === "remove") {
          userData.followings = userData.followings + 1;
        } else {
          userData.followings = userData.followings - 1;
        }
        dispatch(
          setCreatedPostList({
            ...createdPostList,
            data: updatedPosts,
          })
        );

        Toast.show(resp?.data?.message || "No Data found");
        console.log("Something went wrong please try again");
        setFollowLodging({
          loader: false,
          selectedIndex: selected_index,
        });
      }
    } else {
      setIsPaymentModal(true);

      setFollowLodging({
        loader: false,
        selectedIndex: selected_index,
      });
    }
  };

  const UpdateCommentRedux = (post_id) => {
    // Convert the array to a Map for efficient lookups
    const postMap = new Map(
      createdPostList?.data?.map((post) => [post.post_id || post.reel_id, post])
    );

    const post = postMap.get(post_id);

    if (post) {
      post.is_saved = !post.is_saved;

      // Update Map
      postMap.set(post_id, post);

      // Optional: if you also want to update the array
      const updatedPosts = Array.from(postMap.values());

      dispatch(
        setCreatedPostList({
          ...createdPostList,
          data: updatedPosts,
        })
      );
    }
  };

  // save unSave toggle button
  const handleSaveButton = async (post_Id, postType) => {
    const isEligibleForSave =
      !isEmpty(isCurrentPlan) ||
      activePlanData?.is_prime_user ||
      activePlanData?.is_free_user;

    if (isEligibleForSave) {
      // Update the save state immediately without waiting for the API call
      UpdateCommentRedux(post_Id);

      try {
        const resp = await ClickedSaveButton(post_Id, postType);
        if (resp?.data?.success) {
          console.log(resp?.data?.message || "Saved");
        } else {
          // Undo the save state change if something goes wrong with the API response
          UpdateCommentRedux(post_Id);
          Toast.show(resp?.data?.message || "No Data found");
        }
      } catch (error) {
        // Handle error if the API call fails
        UpdateCommentRedux(post_Id);
        Toast.show("Something went wrong");
      }
    } else {
      setIsPaymentModal(true);
    }
  };

  const getSpecialOfferData = async () => {
    const resp = await getSpecialOffer();
    setSliderValue(Number(resp?.data?.progress_percentage || 0));

    if (resp?.data?.success) {
      dispatch(setSpecialOfferData(resp?.data?.data));
      if (isEmpty(reelsList?.data)) {
        getReelList(1);
      }
    } else {
      dispatch(setSpecialOfferData({}));
      if (isEmpty(reelsList?.data)) {
        getReelList(1);
      }
    }
    handleToRemainingPlan();
  };

  const handleToRemainingPlan = async () => {
    const resp = await getRemainingPlanData();
    if (resp !== undefined && resp?.data?.success) {
      if (JSON.stringify(resp?.data?.data) !== JSON.stringify(activePlanData)) {
        dispatch(setActivePlanData(resp?.data?.data));
        dispatch(setAppReviewCount(resp?.data?.data?.ratingCounts));

        if (
          (resp?.data?.data?.is_plan_active === true &&
            resp?.data?.data?.is_prime_user === true) ||
          (resp?.data?.data?.is_plan_active === false &&
            resp?.data?.data?.is_free_user === true) ||
          resp?.data?.data?.plan_status == 1
        ) {
          dispatch(setIsCurrentPlan(resp?.data?.data));
        }
      }

      setCurrentActivePlan(resp?.data?.data);
    } else {
      if (
        resp?.data?.is_plan_active === undefined ||
        resp?.data?.is_plan_active === false
      ) {
        dispatch(setIsCurrentPlan({}));
        dispatch(
          setActivePlanData({
            is_plan_active: false,
            is_prime_user: resp?.data?.data?.is_prime_user || false,
            is_free_user: resp?.data?.data?.is_free_user || false,
            plan_status: resp?.data?.data?.plan_status || 0,
          })
        );
        dispatch(setAppReviewCount(resp?.data?.ratingCounts));
      } else {
        Toast.show(
          resp?.data?.message || "Something went wrong please try again"
        );
      }
    }
  };

  const shortingData = (array, key) => {
    let selfIndex;
    array.forEach((item, index) => {
      if (item.hasOwnProperty(key) && item[key] === true) {
        selfIndex = index;
        return;
      }
    });

    // Move the object with the "self" key to the 0 index
    if (selfIndex !== undefined) {
      const selfObject = array.splice(selfIndex, 1)[0];
      array.unshift(selfObject);
    }

    return array;
  };
  const handleToGetData = async (page = 1) => {
    const respData = await getStoryData(page);
    if (respData?.data?.success) {
      const data = await shortingData(respData?.data?.data, "self");
      if (fromScreenName !== "createStoryScreen") {
        fetchPostData();
      }
      if (respData?.data?.notification_count) {
        dispatch(
          setNotificationCount(Number(respData?.data?.notification_count))
        );
      }
      dispatch(
        setUserStoryList({
          page: 1,
          data: page > 1 ? [...userStoryList?.data, ...data] : data,
          hasNextPage: respData?.data?.hasNextPage,
        })
      );
      dispatch(setFromScreenName(""));
    } else {
      if (fromScreenName !== "createStoryScreen") {
        fetchPostData();
      }
      dispatch(setFromScreenName(""));
      Toast.show(
        respData?.data?.message || "Something went wrong please try again!"
      );
    }
  };

  const getReelList = async (page) => {
    const resp = await getReelsList(page, userData?.user_id);

    if (resp !== undefined && resp?.data?.success && resp?.data?.data) {
      dispatch(
        setReelsList({
          page: page,
          hasNextPage: resp?.data?.hasNextPage,
          currentPage: page,
          data: setReelDataList(page, resp?.data?.data, reelsList, {}),
        })
      );
    }
  };

  const handleToNavigateCompanyDetail = () => {
    if (
      isUndefined(userData?.company_name) ||
      isEmpty(userData?.company_name) ||
      isNull(userData?.company_name)
    ) {
      navigation.navigate("Auth", {
        screen: "SignUpWithFastRegistration",
        params: { id: userData?.user_id, isEdit: true, screen: "Home" },
      });
    } else {
      navigation.navigate("Auth", {
        screen: "SingUpWithFastRegistrationSecond",
        params: { id: userData?.user_id, isEdit: true, screen: "Home" },
      });
    }
  };
  /**
   * Navigate to special offer screen
   *
   * This function navigates to the add on plans screen with special offer data
   * and type as "couponCode".
   */
  const navigateToSpecialOffer = () => {
    navigation.navigate("Auth", {
      screen: "AddOnPlans",
      params: {
        type: "couponCode",
        data: { ...specialOfferData, isSpecialOffer: true },
      },
    });
  };

  const renderStoryData = useCallback(
    (dataType) => {
      return (
        <>
          <StoryComponent />
          {sliderValue < 100 && sliderValue > 0 && (
            <View style={{ margin: 10, marginHorizontal: 20 }}>
              <ProgressBar
                value={sliderValue}
                handleToPressProgress={handleToNavigateCompanyDetail}
              />
            </View>
          )}
          {!isEmpty(recomndaedGroupList) && (
            <TagButtonComponent
              data={recomndaedGroupList}
              navigation={navigation}
            />
          )}
          {isEmpty(isCurrentPlan) &&
          trailTime > 0 &&
          isEmpty(specialOfferData) &&
          activePlanData?.is_free_user ? (
            <TouchableOpacity
              style={styles.trailMainView}
              activeOpacity={0.9}
              onPress={() =>
                navigation.navigate("Auth", {
                  screen: "paymentPlan",
                  params: { isHome: "yes" },
                })
              }
            >
              <Text style={styles.trailTextStyle}>
                Your're using free trial version for{" "}
                {converter.toWords(Number(trailTime))} days.
              </Text>
              <Text
                style={[
                  styles.trailTextStyle,
                  {
                    textDecorationLine: "underline",
                    textTransform: "uppercase",
                  },
                ]}
              >
                GET MEMBERSHIP
              </Text>
            </TouchableOpacity>
          ) : null}
          {/* {isBestOffer &&
          !isEmpty(isBestOffer1) &&
          !activePlanData?.is_prime_user &&
          subscriptionPlan?.filter((item) => item?.is_special_offer === true)[0]
            ?.discount_value > 0 &&
          !isEmpty(isCurrentPlan) &&
          isEmpty(specialOfferData) ? (
            <>
            <OfferComponent specialOfferDataObj={specialOfferData} handleToSpecialOffer={() => handleToSpecialOffer()} />
            </>
          ) : null} */}

          {!isEmpty(specialOfferData) ? (
            <>
              <OfferComponent
                specialOfferDataObj={specialOfferData}
                handleToSpecialOffer={() => navigateToSpecialOffer("best")}
              />
            </>
          ) : dayjs(isCurrentActivePlan?.plan_expire_date).unix() <
              dayjs().unix() &&
            !activePlanData?.is_prime_user &&
            !activePlanData?.is_free_user ? (
            <AlertMessage
              title1="currentPlanIsExpire"
              title2="purchaseNewPlan"
              onBtnPress={() => {
                navigation.navigate("Auth", {
                  screen: "paymentPlan",
                  params: { isHome: "yes" },
                });
              }}
            />
          ) : isCurrentActivePlan?.post_reels_count === 0 &&
            isCurrentActivePlan?.post_count === 0 &&
            isCurrentActivePlan?.allow_unlimited_post === 0 &&
            isCurrentActivePlan?.allow_unlimited_reel === 0 &&
            !activePlanData?.is_prime_user &&
            isEmpty(specialOfferData) ? (
            <AlertMessage
              title1="yourReachPlan"
              title2="upgradeYourPlan"
              onBtnPress={() => {
                navigation.navigate("Auth", {
                  screen: "AddOnPlans",
                  params: {
                    type: "add-ons",
                    data: { isSpecialOffer: false },
                  },
                });
              }}
            />
          ) : null}
          {/* {dayjs(isCurrentActivePlan?.plan_expire_date).unix() <
            dayjs().unix() && !activePlanData?.is_prime_user && !activePlanData?.is_free_user ? (
            <AlertMessage
              title1="currentPlanIsExpire"
              title2="purchaseNewPlan"
              onBtnPress={() => {
                navigation.navigate("Auth", {
                  screen: "paymentPlan",
                  params: { isHome: "yes" },
                });
              }}
            />
          ) : null} */}
          {dataType === "empty" ? (
            <View style={styles.centerMain}>
              <NoRecord title={"noRecordFound"} />
            </View>
          ) : null}
        </>
      );
    },
    [
      isCurrentPlan,
      activePlanData,
      specialOfferData,
      isCurrentActivePlan,
      userData,
    ]
  );

  const renderItem = ({ item, index }) => {
    return (
      <View
        style={{
          marginBottom: index === createdPostList?.data?.length - 1 ? 65 : 0,
        }}
      >
        {index === 0 ? renderStoryData() : null}
        <PostComponent
          item={item}
          index={index}
          handleLike={handleLike}
          setIsLikeAnim={setIsLikeAnim}
          isLikeAnim={isLikeAnim}
          handleToFollow={handleToFollow}
          handleSaveButton={handleSaveButton}
          selectedLikeIndex={selectedLikeIndex}
          setSelectedLikeIndex={setSelectedLikeIndex}
          isFocused={isCurrentFocusIndexMemo === index}
          navigation={navigation}
          onReelPress={(obj) =>
            onReelsPressFunc(navigation, obj, dispatch, setReelsList, reelsList)
          }
          setFollowLodging={setFollowLodging}
          followLodging={followLodging}
          handleToPressBoost={(e, nav) =>
            handleToPressBoost(e, nav, [], [], [])
          }
        />
        {bottomLoading && index === createdPostList?.data?.length - 1 ? (
          <MiniLoader />
        ) : null}
      </View>
    );
  };

  const handleToPressBoost = (
    e,
    nav,
    countryList,
    stateList,
    natureOfBusinessList
  ) => {
    const data = {
      files: e?.type === "post" ? e?.ImageData : e?.ReelData,
      SelectedCountry: e?.country?.split(",").map(Number),
      SelectedState: e?.state?.split(",").map(Number),
      Audience: e?.audience?.split(",").map(Number),
    };

    nav.navigate("CreateBoostPost", {
      data,
      countryData: countryList,
      stateData: stateList,
      natureOfBusinessDada: natureOfBusinessList,
      type: e?.type === "post" ? "post" : "reel",
      apiRes:
        e?.type === "post" ? { post_id: e.post_id } : { reel_id: e.reel_id },
    });
  };
  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    handleToGetData(1);
    apiFunction();
    handleRecomndaedList();
    setTimeout(() => {
      setRefreshing(false);
    }, 2000);
  }, []);

  const flatListRef = useRef(null);

  const renderItemShimmer = ({ item, index }) => {
    return (
      <View
        style={{
          marginBottom: index === 5 - 1 ? 65 : 0,
        }}
      >
        {index === 0 ? (
          isEmpty(userStoryList?.data) ? (
            <ShimmerPlaceholder
              width={75}
              height={75}
              style={{
                marginRight: 42,
                borderRadius: 40,
                marginLeft: 20,
                marginVertical: 20,
              }}
              LinearGradient={LinearGradient}
            />
          ) : (
            <StoryComponent />
          )
        ) : null}
        {isEmpty(isCurrentPlan) &&
        trailTime > 0 &&
        index === 0 &&
        !activePlanData?.is_prime_user &&
        activePlanData?.is_free_user ? (
          <TouchableOpacity
            style={styles.trailMainView}
            activeOpacity={0.9}
            onPress={() =>
              navigation.navigate("Auth", {
                screen: "paymentPlan",
                params: { isHome: "yes" },
              })
            }
          >
            <Text style={styles.trailTextStyle}>
              Your're using free trail version for{" "}
              {converter.toWords(Number(trailTime))} days.
            </Text>
            <Text
              style={[
                styles.trailTextStyle,
                { textDecorationLine: "underline", textTransform: "uppercase" },
              ]}
            >
              GET MEMBERSHIP
            </Text>
          </TouchableOpacity>
        ) : null}

        <ShimmerPostComponent />
      </View>
    );
  };

  const handleToViewInteractionReel = async (index, id) => {
    if (createdPostList?.data[index]?.is_boost === 1) {
      const resp = await handleToInteractionData(id);
    }
  };

  const handleScroll = (event) => {
    const { contentOffset, layoutMeasurement, contentSize } = event.nativeEvent;

    // Check if the user has scrolled close to the bottom
    const isEndReached =
      layoutMeasurement.height + contentOffset.y >= contentSize.height - 20; // Adjust the threshold as needed

    if (isEndReached) {
      if (createdPostList?.next_enable && !bottomLoading) {
        fetchPostData(createdPostList?.page + 1, true);
      }
    }
  };

  return (
    <SafeAreaView style={styles.main}>
      {/* Header */}
      <NavigationDrawerHeader
        navigationProps={navigation}
        logo
        showSearch
        showNotification
        headersStyle={{
          paddingHorizontal: 20,
        }}
        handleSearch={() => navigation.navigate("SearchScreen")}
        handleNotification={() => navigation.navigate("NotificationScreen")}
        notificationCount={notificationCount}
      />

      {/* Post Start */}

      <View style={styles.postListMain}>
        <FlatList
          ref={flatListRef}
          data={createdPostList?.data || []}
          renderItem={renderItem}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
          keyExtractor={(item) => (item?.post_id || item?.reel_id).toString()}
          windowSize={7}
          initialNumToRender={14}
          maxToRenderPerBatch={14}
          updateCellsBatchingPeriod={14}
          onViewableItemsChanged={({ changed }) => {
            const index = changed[0]?.index;
            if (index != null) {
              setIsCurrentFocusIndex(index);

              const item = createdPostList?.data[index];
              handleToViewInteractionReel(
                index,
                item?.type === "post" ? item?.post_id : item?.reel_id
              );

              // 👇 Trigger API call when user scrolls past halfway
              const totalItems = createdPostList?.data?.length || 0;
              if (index >= Math.floor(totalItems / 3) && !bottomLoading) {
                fetchPostData(createdPostList?.page + 1, true); // Your custom API call to append more items
              }
            }
          }}
          refreshing={refreshing}
          onRefresh={onRefresh}
          viewabilityConfig={{
            viewAreaCoveragePercentThreshold: 50,
          }}
          onEndReachedThreshold={3}
          ListEmptyComponent={
            loading ? (
              <View style={styles.postListMain}>
                <FlatList
                  ref={flatListRef}
                  data={[1, 2, 3, 4, 5, 6]}
                  renderItem={renderItemShimmer}
                  keyboardShouldPersistTaps="handled"
                  showsVerticalScrollIndicator={false}
                  keyExtractor={(item, index) => index.toString()}
                  viewabilityConfig={{
                    viewAreaCoveragePercentThreshold: 50,
                  }}
                  onViewableItemsChanged={({ changed }) => {
                    setIsCurrentFocusIndex(changed[0]?.index);
                  }}
                  refreshing={refreshing}
                  onRefresh={onRefresh}
                  onEndReachedThreshold={3}
                  style={{ paddingBottom: 300 }}
                  legacyImplementation
                />
              </View>
            ) : (
              renderStoryData("empty")
            )
          }
          style={{ paddingBottom: 300 }}
          legacyImplementation
        />
      </View>

      <PurChasePlanModal
        visible={isPaymentModal}
        setModalVisible={(e) => setIsPaymentModal(e)}
        text={"currentlyPlanText"}
        navigation={navigation}
      />
    </SafeAreaView>
  );
}
