import React, {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import {
  Image,
  KeyboardAvoidingView,
  SafeAreaView,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
  Keyboard,
} from "react-native";
import CHeader from "@components/CHeader";
import { translate } from "../../lang/Translate";
import * as yup from "yup";
import { Controller, useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { PermissionsAndroid, Platform } from "react-native";
import { PERMISSIONS, check } from "react-native-permissions";
import CInput from "@components/TextInput";
import DateTimePicker from "@components/CDateTimePicker";
import Toast from "react-native-simple-toast";
import CDropdown from "@components/CDropDown";
import CButton from "@components/CButton";
import styles from "./styles";
import { getData, onSubmit, referralApiCall } from "./apiFunctions";
import {
  checkImg,
  cropImage,
  getMobileNumberLength,
  mobileValidation,
  selectAndCropImage,
} from "@app/utils/commonFunction";
import isUndefined from "lodash-es/isUndefined";
import isEmpty from "lodash-es/isEmpty";
import { CustomIcon } from "@config/LoadIcons";
import dayjs from "dayjs";
import authAction from "@redux/reducers/auth/actions";
import { useDispatch, useSelector } from "react-redux";
import { useFocusEffect } from "@react-navigation/native";
import { BaseColors } from "@config/theme";
import ReferCodeModal from "@components/ReferCodeModal";
import AlreadyHaveStoryModal from "@components/AlreadyHaveStoryModal";
import CCamera from "@components/CameraButton/CCamera";

const Animated = require("react-native-reanimated").default;
const FadeInDown = require("react-native-reanimated").FadeInDown;
const { images } = require("@config/images");
const { genderData } = require("@config/staticData");
const FastImage = require("react-native-fast-image");

const { setCompanyId, setUserId, setIsReferralCode } = authAction;
const PersonalDetailSchema = yup.object().shape({
  fullName: yup
    .string()
    .required("enterFullName")
    .matches(/^[A-Za-z]+(\s[A-Za-z]+)*$/, "onlyLettersAndCharacters")
    .min(3, "enterAtLeastTreeCharacter")
    .max(80, "enterMaximumCharacter")
    .trim("whiteSpaceErrMsg"),
  userName: yup
    .string()
    .required("enterYourUsername")
    .matches(/^\S+$/, "usernameWhiteSpace")
    .min(3, "enterAtLeastTreeCharacter")
    .max(80, "enterMaximumCharacter"),
  email: yup.string().required("enterEmailAddress").email("validEmailAddress"),
  mobileNo: yup
    .string()
    .required("enterMobileNumber")
    .matches(/^\d+$/, "onlyNumericValueMsg"),
  password: yup
    .string()
    .required("enterYourPasswordErrMSg")
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&_])[A-Za-z\d@$!%*?&_]{8,}$/,
      "passwordsErrMSg"
    ),

  countryCode: yup.string().default("91"),
  userId: yup.string(),
  referralCode: yup.string(),
});

const PersonalDetailSchemaUpdateTime = yup.object().shape({
  fullName: yup
    .string()
    .required("enterFullName")
    .matches(/^[A-Za-z]+(\s[A-Za-z]+)*$/, "onlyLettersAndCharacters")
    .min(3, "enterAtLeastTreeCharacter")
    .max(80, "enterMaximumCharacter")
    .trim("whiteSpaceErrMsg"),
  userName: yup
    .string()
    .required("enterYourUsername")
    .matches(/^\S+$/, "usernameWhiteSpace")
    .min(3, "enterAtLeastTreeCharacter")
    .max(80, "enterMaximumCharacter"),
  email: yup.string().required("enterEmailAddress").email("validEmailAddress"),
  mobileNo: yup
    .string()
    .required("enterMobileNumber")
    .matches(/^\d+$/, "onlyNumericValueMsg"),
  countryCode: yup.string().default("91"),
  userId: yup.string(),
  referralCode: yup.string(),
});

const PersonalDetailSchemaWithoutPassword = yup.object().shape({
  fullName: yup
    .string()
    .required("enterFullName")
    .matches(/^[A-Za-z]+(\s[A-Za-z]+)*$/, "onlyLettersAndCharacters")
    .min(3, "enterAtLeastTreeCharacter")
    .max(80, "You can enter maximum 15 character")
    .trim("whiteSpaceErrMsg"),
  userName: yup
    .string()
    .required("enterYourUsername")
    .matches(/^\S+$/, "usernameWhiteSpace")
    .min(3, "enterAtLeastTreeCharacter")
    .max(80, "enterMaximumCharacter"),
  email: yup.string().required("enterEmailAddress").email("validEmailAddress"),
  mobileNo: yup.string().required("enterMobileNumber"),
  countryCode: yup.string().default("91"),
  userId: yup.string(),
  referralCode: yup.string(),
});

const SignUpWithPersonalDetail = ({ navigation, route }) => {
  const routeData = route?.params?.data;

  // Redux Variable
  const dispatch = useDispatch();
  const { user_id, isReferralCode, userData } = useSelector((e) => e.auth);

  // useRef :
  const fullNameRef = useRef(null);
  const userNameRef = useRef(null);
  const emailRef = useRef(null);
  const mobileNoRef = useRef(null);
  const passRef = useRef(null);
  const genderRef = useRef(null);

  // State's
  const [mobileNoLength, setMobileLength] = useState(10);
  const [profileImage, setProfileImage] = useState({});
  const [gender, setGender] = useState("male");
  const [isLoading, setIsLoading] = useState(false);
  const [referralCodeModal, setIsReferralModal] = useState(false);
  const [referralCode, setReferralCode] = useState("");
  const [isReferralCodeErr, setIsReferralCodeRrr] = useState("");
  const [countryCode, setCountryCode] = useState("91");
  const [profilePictureModal, setProfilePictureModal] = useState(false);
  const [imagePath, setImagePath] = useState({});
  const [isCameraOpen, setIsCameraOpen] = useState(false);

  // Memo's
  const mobileNoLengthMemo = useMemo(() => mobileNoLength, [mobileNoLength]);
  const isLoadingMemo = useMemo(() => isLoading, [isLoading]);
  const referralCodeModalMemo = useMemo(
    () => referralCodeModal,
    [referralCodeModal]
  );
  const referralCodeMemo = useMemo(() => referralCode, [referralCode]);
  const isReferralCodeErrMemo = useMemo(
    () => isReferralCodeErr,
    [isReferralCodeErr]
  );

  // Form Variable
  const {
    control,
    setValue,
    handleSubmit,
    getValues,
    clearErrors,
    reset,
    setError,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(
      (isEmpty(user_id) && !routeData?.social_connection) ||
        (isEmpty(route?.params?.id) &&
          !routeData?.social_connection &&
          routeData?.social_connection !== undefined)
        ? PersonalDetailSchema
        : routeData?.social_connection &&
            (isEmpty(profileImage) || profileImage === undefined)
          ? PersonalDetailSchemaWithoutPassword
          : PersonalDetailSchemaUpdateTime
    ),
  });

  const handleKeyPress = ({ nativeEvent: { key: keyValue } }) => {
    if (keyValue === "") {
    }
  };

  useEffect(() => {
    setValue("countryCode", "91");
  }, []);

  const pickDocument = async (type) => {
    setIsLoading(true);

    try {
      await requestStoragePermission();
      let resultData = [];
      if (type === "captureImg") {
        resultData = await cropImage(imagePath?.uri, 120, 120, "circle");
        setImagePath({});
      } else {
        resultData = await selectAndCropImage(120, 120);
      }

      let result = [
        {
          fileCopyUri: null,
          name: "images.jpeg",
          size: resultData?.size,
          type: resultData?.mime || "images.jpeg",
          uri: resultData?.path,
        },
      ];
      if (result[0]?.uri) {
        if (checkImg(result[0])?.isValid) {
          setProfileImage(result[0]?.uri);
          setValue("profileImage", result[0]);
          setProfilePictureModal(false);
          setError("profileImage", "");
        } else {
          Toast.show(checkImg(result[0])?.errMsg);
          setProfilePictureModal(false);
        }
        if (
          result[0].type === "image/jpeg" ||
          result[0].type === "image/png" ||
          result[0].type === "image/jpg" ||
          result[0].type === "image/heic"
        ) {
        }

        return result[0];
      }
    } catch (err) {
      console.error("Profile Pick Error ====>", err);
      setProfilePictureModal(false);
      setIsLoading(false);
    } finally {
      setIsLoading(false);
    }
  };

  const requestStoragePermission = async () => {
    if (Platform.OS === "android") {
      PermissionsAndroid.check(
        PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE
      ).then((result) => {
        if (result) {
          console.log("Permission is OK");
        } else {
          PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE
          ).then((res) => {
            if (res) {
            } else {
            }
          });
        }
      });
    } else {
      check(PERMISSIONS.IOS.READ_EXTERNAL_STORAGE).then((res) => {
        if (res !== "granted") {
          console.log("permission granted === ");
        }
      });
    }
  };

  const handleToReferralApiCall = useCallback(async () => {
    setIsLoading(true);
    if (isEmpty(referralCode)) {
      setIsReferralCodeRrr("Please enter your referral code");
      setIsLoading(false);
    } else {
      const resp = await referralApiCall(referralCode);

      if (resp?.data?.success) {
        setValue("referralCode", referralCode);
        setIsLoading(false);
        dispatch(setIsReferralCode(true));
        setIsReferralModal(false);
      } else {
        setIsReferralCodeRrr(resp?.data?.message);
        setReferralCode("");
        setIsLoading(false);
      }
      setIsLoading(false);
    }
  }, [isLoadingMemo, referralCode]);
  const handleToSubmit = useCallback(
    async (data) => {
      const mobileNumberValidation = await mobileValidation(
        data?.mobileNo,
        mobileNoLengthMemo
      );
      if (!mobileNumberValidation?.isValid) {
        setError("mobileNo", {
          message: mobileNumberValidation?.validMessage,
        });
      }
      if (mobileNumberValidation?.isValid) {
        const type = routeData?.social_connection
          ? "social"
          : routeData?.singUpType || userData?.type || "email";
        const googleId = routeData?.social_connection
          ? routeData?.googleId
          : null;
        setIsLoading(true);
        const respData = await onSubmit(
          data,
          type,
          googleId,
          route?.params?.isEdit || false
        );
        if (respData?.data?.success) {
          if (isEmpty(user_id)) {
            dispatch(setCompanyId(respData?.data?.data?.company_id));
            dispatch(setUserId(respData?.data?.data?.user_id));
          }
          navigation.navigate("SingUpWithFastRegistrationSecond", {
            user_id: respData?.data?.data?.user_id,
            company_id: respData?.data?.data?.company_id,
            isEdit: route?.params?.isEdit || false,
          });
          reset();
          setProfileImage({});
          setIsLoading(false);
        } else {
          if (respData?.data?.message === "Network error") {
            handleToSubmit(data);
          } else {
            Toast.show(respData?.data?.message);
            setIsLoading(false);
          }
        }

        setIsLoading(false);
      }
    },
    [isLoadingMemo]
  );

  const getUserData = useCallback(async () => {
    setIsLoading(true);
    const respData = await getData(user_id || route?.params?.id);

    if (respData?.data?.success && respData?.data?.data) {
      setValue("fullName", respData?.data?.data?.full_name);

      setValue("gender", respData?.data?.data?.gender);
      setValue("dateOfBirth", respData?.data?.data?.dob);
      setValue("countryCode", String(respData?.data?.data?.phone_code));
      setCountryCode(String(respData?.data?.data?.phone_code));
      setValue("mobileNo", String(respData?.data?.data?.phone_number));
      setValue("email", respData?.data?.data?.email);
      setValue("userName", respData?.data?.data?.username);
      setValue("userId", respData?.data?.data?.user_id);
      setValue("profileImage", respData?.data?.data?.profile_picture_name);
      setProfileImage(respData?.data?.data?.profile_picture || {});
      const country_length = await getMobileNumberLength(
        respData?.data?.data?.sortname
      );
      setMobileLength(country_length);
    } else {
      setIsLoading(false);
    }
    setIsLoading(false);
  }, [isLoadingMemo, user_id]);

  useFocusEffect(
    useCallback(() => {
      if (!isEmpty(user_id) || !isEmpty(route?.params?.id)) {
        getUserData();
      }
      if (!isEmpty(routeData) || !isUndefined(routeData)) {
        handleToSetValue();
      }
      if (!isReferralCode) {
        if (Platform.OS === "ios") {
          setTimeout(() => {
            setIsReferralModal(true);
          }, 1000);
        } else {
          setIsReferralModal(true);
        }
      }
    }, [user_id, routeData])
  );

  const handleToSetValue = () => {
    if (routeData?.singUpType === "phone") {
      setValue("mobileNo", routeData?.text);
      setValue("countryCode", routeData?.countryCode);
    } else if (routeData?.social_connection) {
      setValue("fullName", routeData?.name);
      setValue("email", routeData?.email);
      setValue("profileImage", routeData?.profile);
      setProfileImage(routeData?.profile);
    } else {
      setValue("email", routeData?.text);
    }
  };

  useEffect(() => {
    if (isCameraOpen) {
      setProfilePictureModal(false);
    }
    if (!isCameraOpen && imagePath?.uri) {
      // setProfilePictureModal(false);
      pickDocument("captureImg");
    }
  }, [isCameraOpen]);

  return (
    <SafeAreaView style={styles.mainView}>
      {/* Camera open component */}
      {isCameraOpen ? (
        <View style={{ flex: 1 }}>
          <CCamera
            setImagePath={setImagePath}
            isCameraOpen={isCameraOpen}
            setIsCameraOpen={setIsCameraOpen}
            recordVideo={false}
          />
        </View>
      ) : (
        <>
          <CHeader handleBackButton={() => navigation.goBack()} />
          <KeyboardAvoidingView
            behavior={Platform.OS === "ios" ? "padding" : null}
            style={styles.KeyboardAvoidingViewStyle}
          >
            <ScrollView showsVerticalScrollIndicator={false} bounces={false}>
              <Image
                source={images.footMainLogo}
                style={styles.mainFootLogo}
                resizeMode={"contain"}
              />
              <Text style={styles.headingText}>
                {translate("letsCreateAccountText")}
              </Text>
              <TouchableOpacity
                style={styles.profileImgMainViewStyle}
                activeOpacity={0.8}
                onPress={() => setProfilePictureModal(true)}
              >
                {isEmpty(profileImage) ? (
                  <View style={styles.profileImgMainView}>
                    <FastImage
                      source={
                        gender === "male"
                          ? images.manAvatar
                          : gender === "female"
                            ? images.womanAvatar
                            : images.manAvatar
                      }
                      style={styles.profileImgStyle}
                    />
                  </View>
                ) : (
                  <View style={styles.profileImgMainView}>
                    <Image
                      source={{ uri: profileImage }}
                      style={styles.profileImgStyle}
                    />
                  </View>
                )}
                <TouchableOpacity
                  style={styles.editMainViewStyle}
                  activeOpacity={0.9}
                  onPress={async () => setProfilePictureModal(true)}
                >
                  <CustomIcon
                    name={"Edit-Square"}
                    size={25}
                    color={BaseColors.activeTab}
                  />
                </TouchableOpacity>
              </TouchableOpacity>
              <Text style={styles.profileTextStyle}>
                {translate("profilePhotoText")}
              </Text>

              {errors?.profileImage?.message ? (
                <View style={styles.errorMsgMainView}>
                  <CustomIcon
                    name={"BsExclamationCircle"}
                    size={18}
                    color={"#D6002E"}
                  />
                  <Text style={styles.errorTxt}>
                    {errors?.profileImage?.message}
                  </Text>
                </View>
              ) : null}
              {/* Form View */}
              <View style={styles.formMainView}>
                <Controller
                  control={control}
                  rules={{
                    required: true,
                  }}
                  name="fullName"
                  render={({ field: { onChange, value } }) => (
                    <Animated.View
                      style={styles.commonView}
                      entering={FadeInDown}
                    >
                      <CInput
                        reference={fullNameRef}
                        placeholderText={translate("fullNameText")}
                        returnKeyType="next"
                        value={value}
                        onChange={onChange}
                        onSubmit={() => {
                          userNameRef.current.focus();
                        }}
                        isError={errors?.fullName}
                        isErrorMsg={errors?.fullName?.message}
                      />
                    </Animated.View>
                  )}
                />
                <Controller
                  control={control}
                  rules={{
                    required: true,
                  }}
                  name="userName"
                  render={({ field: { onChange, value } }) => (
                    <Animated.View
                      style={styles.commonView}
                      entering={FadeInDown}
                    >
                      <CInput
                        reference={userNameRef}
                        placeholderText={translate("userNameText")}
                        returnKeyType="next"
                        value={value}
                        onChange={onChange}
                        maxLength={150}
                        onSubmit={() => {
                          emailRef.current.focus();
                        }}
                        isError={errors?.userName}
                        isErrorMsg={errors?.userName?.message}
                      />
                    </Animated.View>
                  )}
                />
                <Controller
                  control={control}
                  rules={{
                    required: true,
                  }}
                  name="email"
                  render={({ field: { onChange, value } }) => (
                    <Animated.View
                      style={styles.commonView}
                      entering={FadeInDown}
                    >
                      <CInput
                        reference={emailRef}
                        placeholderText={translate("EmailText")}
                        returnKeyType="next"
                        value={value}
                        onChange={(e) => {
                          const sanitizedText = e.replace(/\s+/g, "");
                          onChange(sanitizedText);
                        }}
                        onSubmit={() => {
                          mobileNoRef.current.focus();
                        }}
                        isError={errors?.email}
                        isErrorMsg={errors?.email?.message}
                        disabled={
                          (routeData?.singUpType !== "phone" &&
                            (!isEmpty(routeData?.text) ||
                              !isEmpty(routeData?.email))) ||
                          userData?.type === "email" ||
                          userData?.type === "social"
                            ? true
                            : false
                        }
                      />
                    </Animated.View>
                  )}
                />
                <Controller
                  control={control}
                  rules={{
                    required: true,
                  }}
                  name="mobileNo"
                  render={({ field: { onChange, value } }) => (
                    <Animated.View
                      style={styles.commonView}
                      entering={FadeInDown}
                    >
                      <CInput
                        reference={mobileNoRef}
                        placeholderText={translate("MobileNumberPlaceHolder")}
                        returnKeyType="next"
                        value={value}
                        onChange={async (e) => {
                          const sanitizedText = e.replace(/\s+/g, "");
                          onChange(sanitizedText);
                          const mobileNumberValidation = await mobileValidation(
                            e,
                            mobileNoLengthMemo
                          );
                          if (!mobileNumberValidation?.isValid) {
                            setError("mobileNo", {
                              message: mobileNumberValidation?.validMessage,
                            });
                          } else if (mobileNumberValidation?.isValid) {
                            clearErrors("mobileNo");
                          }
                        }}
                        onSubmit={() => {
                          clearErrors("mobileNo");
                          isEmpty(user_id) || isEmpty(route?.params?.id)
                            ? passRef.current.focus()
                            : Keyboard.dismiss();
                        }}
                        keyBoardType="number-pad"
                        maxLength={mobileNoLengthMemo}
                        inputType="mobile"
                        isError={errors?.mobileNo}
                        isErrorMsg={errors?.mobileNo?.message}
                        selectedCountryCode={(e) => {
                          const country_length = getMobileNumberLength(
                            e?.country_code
                          );
                          setMobileLength(country_length);
                          setValue("mobileNo", undefined);
                          const yourData = e?.dial_code?.split("+");
                          setCountryCode(yourData);
                          setValue("countryCode", yourData[1]);
                        }}
                        disabled={
                          (routeData?.singUpType === "phone" &&
                            !isEmpty(routeData?.text)) ||
                          userData?.type === "phone"
                            ? true
                            : false
                        }
                        onKeyPress={handleKeyPress}
                        countryCodeValue={
                          getValues("countryCode")
                            ? `+${getValues("countryCode")}`
                            : `+${countryCode}`
                        }
                      />
                    </Animated.View>
                  )}
                />
                {isEmpty(user_id) && !routeData?.social_connection ? (
                  <Controller
                    control={control}
                    rules={{
                      required:
                        isEmpty(user_id) && !routeData?.social_connection
                          ? true
                          : false,
                    }}
                    name="password"
                    render={({ field: { onChange, value } }) => (
                      <Animated.View
                        style={styles.commonView}
                        entering={FadeInDown}
                      >
                        <CInput
                          reference={passRef}
                          placeholderText={translate("passWordText")}
                          returnKeyType="next"
                          value={value}
                          passwordInputField={true}
                          onChange={(e) => {
                            const sanitizedText = e.replace(/\s+/g, "");
                            onChange(sanitizedText);
                          }}
                          onKeyPress={(e) => {
                            handleKeyPress(e);
                          }}
                          onSubmit={() => {
                            // passwordText.current.focus();
                          }}
                          isError={errors?.password}
                          isErrorMsg={errors?.password?.message}
                        />
                      </Animated.View>
                    )}
                  />
                ) : null}
                <Controller
                  control={control}
                  rules={{
                    required: true,
                  }}
                  name="dateOfBirth"
                  render={({ field: { onChange, value } }) => (
                    <View style={styles.commonView}>
                      <DateTimePicker
                        type={"date"}
                        placeholder={translate("dateOfBirthText")}
                        value={value}
                        onConfirm={onChange}
                        maximumDate={dayjs().subtract(18, "year").toDate()}
                        logo={"dateTime"}
                        title={"Select Date"}
                        isError={errors?.dateOfBirth}
                        isErrorMsg={errors?.dateOfBirth?.message}
                      />
                    </View>
                  )}
                />

                <Controller
                  control={control}
                  rules={{
                    required: false,
                  }}
                  name="gender"
                  render={({ field: { onChange, value } }) => (
                    <Animated.View
                      entering={FadeInDown}
                      style={[styles.commonView, { marginBottom: 40 }]}
                    >
                      <CDropdown
                        labelplaceholder={translate("genderText")}
                        data={genderData}
                        setItem={(e) => {
                          onChange(e?.value);
                          setGender(e?.value);
                        }}
                        value={value}
                        isError={errors?.gender}
                        isErrorMsg={errors?.gender?.message}
                        position={"top"}
                      />
                    </Animated.View>
                  )}
                />
              </View>
            </ScrollView>
            <CButton
              style={styles.buttonView}
              onBtnClick={handleSubmit(handleToSubmit)}
              loading={isLoadingMemo}
            >
              {translate("nextText")}
            </CButton>
            <ReferCodeModal
              visible={referralCodeModalMemo}
              setModalVisible={() => setIsReferralModal(!referralCodeModalMemo)}
              setReferralCode={(e) => {
                (setReferralCode(e), setIsReferralCodeRrr(""));
              }}
              referralValue={referralCodeMemo}
              isError={isReferralCodeErrMemo}
              onSubmit={() => handleToReferralApiCall()}
              isLoading={isLoadingMemo}
            />
            {profilePictureModal ? (
              <AlreadyHaveStoryModal
                visible={profilePictureModal}
                setModalVisible={(e) => setProfilePictureModal(e)}
                title1="captureFromCamera"
                title2="chooseFromGallery"
                onPressTitle1={() => setIsCameraOpen(true)}
                onPressTitle2={() => pickDocument("selectImg")}
              />
            ) : null}
          </KeyboardAvoidingView>
        </>
      )}
    </SafeAreaView>
  );
};
export default memo(SignUpWithPersonalDetail);
