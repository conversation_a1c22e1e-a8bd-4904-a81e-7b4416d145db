import {
  ActivityIndicator,
  FlatList,
  Platform,
  SafeAreaView,
  ScrollView,
  Text,
  View,
} from "react-native";
import React, { useEffect, useState } from "react";
import CHeader from "@components/CHeader";
import CSearch from "@components/CSearch";
import styles from "./styles";
import NoRecord from "@components/NoRecord";
import RenderSearchData from "./RenderSearchData";
import { menuSearchData } from "@config/staticData";
import { BaseColors } from "@config/theme";
import { TouchableOpacity } from "react-native";
import { FontFamily } from "@config/typography";
import FastImage from "react-native-fast-image";
import LottieView from "lottie-react-native";
import { images } from "@config/images";
import { isEmpty, isNull } from "lodash-es";
import MiniLoader from "@components/MiniLoader";
import { getChatSearchData } from "./apiCallFunction";
import Toast from "react-native-simple-toast";
import { useDispatch, useSelector } from "react-redux";
import AuthActions from "@redux/reducers/auth/actions";
import { ResponsiveGrid } from "react-native-flexible-grid";
import { onReelsPressFunc } from "@components/StoryComponent/apiCallFunction";
import { groupRequestList } from "@screens/Chat/apiCallFunction";
import PurChasePlanModal from "@components/PurchasePlanModal";

const { setReelsList, setSavedPostList } = AuthActions;

const SearchScreen = ({ navigation, route }) => {
  const dispatch = useDispatch();
  const searchBtnData = route?.params?.searchBtnData || "";
  const [selectedTabIndex, setSelectedTabIndex] = useState(0);
  const [loading, setLoading] = useState(false);
  const [forYouData, setForYouData] = useState([]);
  const [groupsData, setGroupsData] = useState([]);
  const [accountData, setAccountData] = useState([]);
  const [reelData, setReelData] = useState([]);
  const [bottomLoading, setBottomLoading] = useState(false);
  const [addLoading, setAddLoading] = useState(false);
  const [isPaymentModal, setIsPaymentModal] = useState(false);
  const [requestId, setRequestId] = useState("");

  const { userData, reelsList, isCurrentPlan, activePlanData } = useSelector(
    (auth) => auth.auth
  );

  useEffect(() => {
    if (route?.params?.groupSearch) {
      setSelectedTabIndex(3);
    }
  }, []);

  useEffect(() => {
    setBottomLoading(false);
    setLoading(true);
    handleSearchList(searchBtnData, 1, false);
  }, [route, selectedTabIndex, searchBtnData]);

  const handleSearchList = async (
    searchBtnData = "",
    page = 1,
    bottomLoader = false
  ) => {
    if (bottomLoader) {
      setBottomLoading(true);
    }

    // Search Type
    let searchType =
      selectedTabIndex === 2
        ? "reels"
        : selectedTabIndex === 1
          ? "account"
          : selectedTabIndex === 3
            ? "group"
            : "for_me";

    const resp = await getChatSearchData(
      searchBtnData?.trim(),
      page,
      searchType
    );

    if (resp?.data?.success && !isEmpty(resp?.data?.data)) {
      if (selectedTabIndex === 1) {
        setAccountData({
          page: page,
          next_enable: resp?.data?.pagination?.hasNextPage || false,
          data:
            page > 1
              ? [...accountData?.data, ...resp?.data?.data]
              : resp?.data?.data,
        });
      } else if (selectedTabIndex === 2) {
        let modifiedArray = resp?.data?.data?.map((obj) => {
          return {
            ...obj,
            widthRatio: 1,
            heightRatio: 2,
          };
        });
        setReelData({
          page: page,
          next_enable: resp?.data?.pagination?.hasNextPage || false,
          data:
            page > 1 ? [...reelData?.data, ...modifiedArray] : modifiedArray,
        });
      } else if (selectedTabIndex === 3) {
        setGroupsData({
          page: page,
          next_enable: resp?.data?.pagination?.hasNextPage || false,
          data:
            page > 1
              ? [...groupsData?.data, ...resp?.data?.data]
              : resp?.data?.data,
        });
      } else {
        let modifiedArray = resp?.data?.data?.map((obj) => {
          if (obj?.type === "reel") {
            return {
              ...obj,
              widthRatio: 1,
              heightRatio: 2,
            };
          } else {
            return obj;
          }
        });
        setForYouData({
          page: page,
          next_enable: resp?.data?.pagination?.hasNextPage || false,
          data:
            page > 1 ? [...forYouData?.data, ...modifiedArray] : modifiedArray,
        });
      }
      setLoading(false);
      setBottomLoading(false);
    } else {
      if (selectedTabIndex === 1) {
        setAccountData([]);
      } else if (selectedTabIndex === 2) {
        setReelData([]);
      } else {
        setForYouData([]);
      }
      setLoading(false);
      setBottomLoading(false);
      Toast.show(resp?.data?.message || "No Data Found");
    }
  };

  // navigate to user Profile
  const handleToPressUser = (data) => {
    if (data?.user_id !== userData?.user_id) {
      navigation.navigate("ProfileNew", { data: data, type: "anotherProfile" });
    } else {
      navigation.navigate("ProfileNew", { data: data });
    }
  };

  const renderSearchView = ({ item }) => (
    <View style={{ flex: 1, alignItems: "center", justifyContent: "center" }}>
      <RenderSearchData
        item={item}
        onPress={(pressData) => handleToPressUser(pressData)}
      />
    </View>
  );

  const handleTabPress = (index) => {
    setSelectedTabIndex(index);
  };

  const renderTabMenu = ({ item, index }) => (
    <TouchableOpacity
      style={[
        styles.tabList,
        {
          backgroundColor:
            selectedTabIndex === index
              ? BaseColors.activeTab
              : BaseColors.white,
        },
      ]}
      onPress={() => handleTabPress(index)}
      activeOpacity={0.8}
    >
      <Text
        style={{
          color:
            selectedTabIndex === index ? BaseColors.white : BaseColors.gray9,
          fontSize: 13.5,
          fontFamily: FontFamily.InterSemiBold,
        }}
      >
        {item?.name}
      </Text>
    </TouchableOpacity>
  );

  const renderItem = ({ item, index }) => {
    return (
      <TouchableOpacity
        activeOpacity={0.8}
        onPress={() => {
          if (item?.type === "post") {
            let allPostList = {
              data: [item],
              screenName: "fromSearchScreen",
            };
            dispatch(setSavedPostList(allPostList));
            navigation.navigate("SavedPostList", {
              screenName: "fromSearchScreen",
            });
          } else {
            console.log("reelData=====>", item, reelsList);
            onReelsPressFunc(
              navigation,
              item,
              dispatch,
              setReelsList,
              reelsList
            );
          }
        }}
        style={{ borderWidth: 1, borderColor: BaseColors.white }}
      >
        {item?.ImageData || item?.ReelData ? (
          <FastImage
            source={{
              uri:
                item?.type === "post"
                  ? item?.ImageData[0]?.fileUrl
                  : item?.ReelData?.thumbnailData?.thumbUrl,
            }}
            style={[
              styles.image,
              {
                height: "100%",
                width: "100%",
              },
            ]}
            resizeMode={"cover"}
          />
        ) : null}

        {item?.type === "post" ? null : (
          <View style={styles.addReelIcon}>
            <LottieView
              autoSize={true}
              source={images.reelAnim}
              autoPlay={true}
              loop
              style={styles.reelAnimView}
            />
          </View>
        )}
      </TouchableOpacity>
    );
  };

  const handleScroll = (event) => {
    const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;
    const isCloseToBottom =
      layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;

    let bottomLoader = true;

    if (selectedTabIndex === 0) {
      if (isCloseToBottom && forYouData?.next_enable) {
        handleSearchList(searchBtnData, forYouData?.page + 1, bottomLoader);
      }
    } else if (selectedTabIndex === 1) {
      if (isCloseToBottom && accountData?.next_enable) {
        handleSearchList(searchBtnData, accountData?.page + 1, bottomLoader);
      }
    } else if (selectedTabIndex === 2) {
      if (isCloseToBottom && reelData?.next_enable) {
        handleSearchList(searchBtnData, reelData?.page + 1, bottomLoader);
      }
    }
  };

  const handleRequestToJoin = async (group_details_id) => {
    setAddLoading(true);
    const resp = await groupRequestList(group_details_id);
    if (resp?.success) {
      setAddLoading(false);
      setRequestId("");
      await handleSearchList(searchBtnData, 1);
    } else {
      setAddLoading(false);
    }
  };

  const RenderDataItem = ({ item }) => {
    return (
      <TouchableOpacity
        style={styles.renderItemMainView}
        activeOpacity={0.8}
        disabled={item?.is_member === 1 ? false : true}
      >
        {/* Image */}
        <View>
          <FastImage source={{ uri: item?.imageUrl }} style={styles.imgView} />
        </View>

        {/* User Titles */}
        <View style={styles.titleView}>
          {item?.group_name ? (
            <Text style={styles.userNameViewText}>{item?.group_name}</Text>
          ) : null}

          {/* Data and time */}
          <TouchableOpacity
            style={{
              paddingVertical: 5,
              paddingHorizontal: 10,
              borderWidth: 1,
              borderRadius: 3,
              borderColor:
                item?.is_request_for_group === 1
                  ? BaseColors.green
                  : BaseColors.activeTab,
              position: "absolute",
              right: 0,
              top: 0,
            }}
            onPress={() => {
              if (
                isEmpty(isCurrentPlan) &&
                !activePlanData?.is_prime_user &&
                !activePlanData?.is_free_user
              ) {
                setIsPaymentModal(true);
              } else {
                setRequestId(item?.group_details_id);
                handleRequestToJoin(item?.group_details_id);
              }
            }}
            disabled={item?.is_request_for_group === 1 ? true : false}
          >
            {addLoading && requestId === item?.group_details_id ? (
              <ActivityIndicator
                animating
                color={BaseColors.activeTab}
                size="small"
              />
            ) : (
              <Text
                style={{
                  fontSize: 14,
                  fontFamily: FontFamily.RobotoMedium,
                  color:
                    item?.is_request_for_group === 1
                      ? BaseColors.green
                      : BaseColors.activeTab,
                }}
              >
                {item?.is_request_for_group === 1 ? "Requested" : "Add"}
              </Text>
            )}
          </TouchableOpacity>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <SafeAreaView style={styles.main}>
      {/* Header */}
      <CHeader
        handleBackButton={() => navigation.goBack()}
        headingTitle="Search"
      />

      {/* Search  */}
      <View style={styles.searchView}>
        <CSearch
          searchBtnData={searchBtnData}
          searchLeft
          searchDisable
          handleSearchNavigation={() =>
            navigation.navigate("SearchInput", {
              searchBtnData: !isEmpty(searchBtnData) ? searchBtnData : "",
            })
          }
        />
      </View>

      <View style={styles.tabView}>
        <FlatList
          data={menuSearchData}
          renderItem={renderTabMenu}
          showsVerticalScrollIndicator={false}
          horizontal={true}
        />
      </View>
      <ScrollView
        onScroll={handleScroll}
        scrollEventThrottle={16}
        keyboardShouldPersistTaps="handled"
        // refreshControl={
        //   <RefreshControl
        //     refreshing={refreshing}
        //     onRefresh={onRefresh}
        //     colors={[BaseColors.activeTab]} // Customize refresh indicator color
        //     tintColor={BaseColors.activeTab} // Customize refresh indicator color (Android)
        //   />
        // }
        showsVerticalScrollIndicator={false}
      >
        {loading ? (
          <View style={styles.centerEmpty}>
            <MiniLoader size="medium" />
          </View>
        ) : (
          <View style={{ flex: 1 }}>
            {/* Reel And Post Grid */}
            {selectedTabIndex === 0 ? (
              !isEmpty(forYouData?.data) ? (
                <ResponsiveGrid
                  maxItemsPerColumn={3}
                  data={forYouData?.data || []}
                  renderItem={renderItem}
                  showScrollIndicator={false}
                  keyExtractor={(item) => item?.id?.toString()}
                />
              ) : (
                <View style={styles.centerEmpty}>
                  <NoRecord title={"noRecordFound"} />
                </View>
              )
            ) : null}

            {/* Reel Grid */}
            {selectedTabIndex === 2 ? (
              !isEmpty(reelData?.data) ? (
                <ResponsiveGrid
                  maxItemsPerColumn={3}
                  data={reelData?.data || []}
                  renderItem={renderItem}
                  showScrollIndicator={false}
                  keyExtractor={(item) => item?.id?.toString()}
                />
              ) : (
                <View style={styles.centerEmpty}>
                  <NoRecord title={"noRecordFound"} />
                </View>
              )
            ) : null}

            {/* Search List */}
            {selectedTabIndex === 1 ? (
              !isEmpty(accountData?.data) ? (
                <View style={styles.mainPostView}>
                  <FlatList
                    data={accountData?.data}
                    renderItem={renderSearchView}
                    showsVerticalScrollIndicator={false}
                    style={{ marginBottom: Platform.OS === "ios" ? 50 : 0 }}
                  />
                </View>
              ) : (
                <View style={styles.centerEmpty}>
                  <NoRecord title={"noRecordFound"} />
                </View>
              )
            ) : null}

            {selectedTabIndex === 3 ? (
              <FlatList
                data={groupsData?.data || []}
                renderItem={(item) => {
                  return <RenderDataItem item={item?.item} />;
                }}
                showsVerticalScrollIndicator={false}
                keyboardShouldPersistTaps="handled"
                ListEmptyComponent={
                  <View style={styles.centerMain}>
                    <NoRecord title={"noRecordFound"} />
                  </View>
                }
                style={{ marginBottom: Platform.OS === "ios" ? 50 : 0 }}
              />
            ) : null}
          </View>
        )}
        {bottomLoading ? (
          <View>
            <MiniLoader size="small" />
          </View>
        ) : null}
      </ScrollView>
      <PurChasePlanModal
        visible={isPaymentModal}
        setModalVisible={(e) => setIsPaymentModal(e)}
        text={"currentlyPlanText"}
        navigation={navigation}
      />
    </SafeAreaView>
  );
};

export default SearchScreen;
