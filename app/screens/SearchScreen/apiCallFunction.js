import { getApiData } from "@app/utils/apiHelper";
import BaseSetting from "@config/setting";
import Toast from "react-native-simple-toast";

// Search  List
export const getChatSearchData = async (searchBtnData, page, searchType) => {
  // if (!isEmpty(searchBtnData?.trim())) {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.searchList}?slug=${searchType}&page=${page}&pageSize=17&search=${encodeURIComponent(searchBtnData)}`,
      "GET"
    );

    if (resp !== undefined) {
      return resp;
    } else {
      Toast.show(resp?.data?.message || "No Data Found");
    }
  } catch (error) {
    console.error("🚀 ~ getData ~ error:", error);
  }
  // } else {
  //   return [];
  // }
};
