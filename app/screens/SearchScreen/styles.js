import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";
import { StyleSheet } from "react-native";

const styles = StyleSheet.create({
  main: {
    flex: 1,
    backgroundColor: BaseColors.white,
  },
  searchView: {
    marginHorizontal: 20,
  },
  centerMain: {
    flex: 1,
    marginTop: HEIGHT / 5,
  },
  centerEmpty: {
    height: HEIGHT - 180,
  },
  centerMainEmpty: {
    flex: 1,
  },
  tabText: {},
  tabView: {
    marginHorizontal: 20,
    marginVertical: 20,
  },
  tabMain: {
    backgroundColor: BaseColors.activeTab,
    flexDirection: "row",
  },
  tabList: {
    flex: 1,
    marginRight: 10,
    padding: 5,
    borderRadius: 5,
    paddingHorizontal: 10,
  },
  mainPostView: {
    marginTop: 10,
    flex: 1,
  },
  imageContainer: {
    marginRight: 5,
    marginBottom: 5,
    overflow: "hidden",
    elevation: 6,
    position: "relative",
    backgroundColor: "red",
  },
  image: {
    width: "100%",
  },
  addReelIcon: {
    paddingRight: 5,
    paddingTop: 5,
    position: "absolute",
    right: -15,
    top: -15,
  },
  reelAnimView: {
    height: 60,
    width: 60,
  },
  listContainer: {
    padding: 10,
  },
  itemContainer: {
    flex: 1,
    margin: 5,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#f0f0f0",
  },
  itemText: {
    fontSize: 18,
    color: "red",
  },
  renderItemMainView: {
    flex: 1,
    flexDirection: "row",
    padding: 10,
    alignItems: "center",
    marginHorizontal: 10,
  },
  imgView: {
    height: 55,
    width: 55,
    borderRadius: 33,
  },
  titleView: {
    flex: 1,
    marginLeft: 16,
  },
  userNameViewText: {
    fontSize: 17,
    fontFamily: FontFamily.InterSemiBold,
    color: BaseColors.black,
    textTransform: "capitalize",
  },
});
export default styles;
