import React, { memo, useCallback, useMemo, useRef, useState } from "react";
import {
  Text,
  View,
  useWindowDimensions,
  StatusBar,
  Platform,
  Dimensions,
} from "react-native";
import Pagination from "./Pagination";
import styles from "./styles";
import CButton from "@components/CButton";
import FastImage from "react-native-fast-image";
import { translate } from "../../lang/Translate";
import { useFocusEffect } from "@react-navigation/native";
import { getData } from "./apiFunctions";
import CLoader from "@components/CLoader";
import isEmpty from "lodash-es/isEmpty";
import authAction from "@redux/reducers/auth/actions";
import { useDispatch } from "react-redux";

const Animated = require("react-native-reanimated").default;
const useSharedValue = require("react-native-reanimated").useSharedValue;
const useAnimatedScrollHandler =
  require("react-native-reanimated").useAnimatedScrollHandler;
const useAnimatedRef = require("react-native-reanimated").useAnimatedRef;

const { setIsIntro } = authAction;

const Intro = ({ navigation }) => {
  // Redux Variable
  const dispatch = useDispatch();
  // State's
  const [data, setData] = useState([]);

  // Memo's
  const dataMemo = useMemo(() => data, [data]);

  // Width Variable
  const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = useWindowDimensions();

  // Animation Variable
  const flatListRef = useAnimatedRef(null);
  const x = useSharedValue(0);
  const flatListIndex = useSharedValue(0);

  // Animation Function
  const onScroll = useAnimatedScrollHandler({
    onScroll: (event) => {
      x.value = event.contentOffset.x;
    },
  });

  // Get Intro Screen Data
  const getIntroScreenData = async () => {
    const mainData = await getData();
    setData(mainData);
  };

  useFocusEffect(
    useCallback(() => {
      getIntroScreenData();
    }, []),
  );

  // Button Click Event when user press let's started button then
  const handleToLogin = useCallback(() => {
    dispatch(setIsIntro(false));
    navigation.replace("Auth");
  }, []);

  // RenderItem Function
  const RenderItem = useCallback(({ item, index }) => {
    return (
      <View style={styles.itemContainer}>
        <FastImage
          source={{ uri: item.content_url }}
          style={[
            {
              width: SCREEN_WIDTH,
              height:
                Platform.OS === "ios"
                  ? Dimensions.get("window").height < 800
                    ? SCREEN_HEIGHT / 1.75
                    : SCREEN_HEIGHT / 1.6
                  : SCREEN_HEIGHT - 300,
              marginLeft: 0,
            },
          ]}
          resizeMode="cover"
        />

        <Text
          style={[
            styles.itemTitle,

            {
              width: SCREEN_WIDTH * 1,
            },
          ]}
          numberOfLines={index === 2 || 1 ? 3 : 2}>
          {item.title}
        </Text>
        <Text
          style={[styles.descTextStyle, { width: SCREEN_WIDTH * 1 }]}
          numberOfLines={index === 2 || 1 ? 3 : 2}>
          {item.description}
        </Text>
      </View>
    );
  }, []);
  const onViewableItemsChanged2 = useCallback(({ viewableItems }) => {
    if (viewableItems.length > 0) {
      flatListIndex.value = viewableItems[0].index;
    }
  }, []);
  const viabilityConfigCallbackPairs = useRef([{ onViewableItemsChanged2 }]);

  return (
    <View style={styles.container}>
      <StatusBar translucent backgroundColor="transparent" />
      {isEmpty(dataMemo) ? (
        <CLoader />
      ) : (
        <View>
          <Animated.FlatList
            ref={flatListRef}
            onScroll={onScroll}
            data={data}
            contentContainerStyle={{}}
            renderItem={({ item, index }) => {
              return <RenderItem item={item} index={index} />;
            }}
            keyExtractor={(item) => item.id}
            scrollEventThrottle={16}
            horizontal={true}
            bounces={false}
            pagingEnabled={true}
            showsHorizontalScrollIndicator={false}
            viewabilityConfigCallbackPairs={
              viabilityConfigCallbackPairs.current
            }
            viewabilityConfig={{
              minimumViewTime: 300,
              viewAreaCoveragePercentThreshold: 50,
            }}
          />
        </View>
      )}
      <View style={styles.bottomMainViewStyle}>
        <View style={styles.paginationView}>
          <Pagination
            data={dataMemo ? dataMemo : []}
            x={x}
            screenWidth={SCREEN_WIDTH}
          />
        </View>
        <View style={styles.buttonView}>
          <CButton onBtnClick={() => handleToLogin()}>
            {translate("letStartBtnText")}
          </CButton>
        </View>
      </View>
    </View>
  );
};

export default memo(Intro);
