import React, {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import {
  Dimensions,
  FlatList,
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
  ScrollView,
  Switch,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import styles from "./styles";
import CInput from "@components/TextInput";
import CButton from "@components/CButton";
import CHeader from "@components/CHeader";
import { translate } from "../../lang/Translate";
import * as yup from "yup";
import { Controller, useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import {
  onsubmit,
  getCountryData,
  getCountryStateData,
  natureOfBusinessData,
} from "./apiFunctions";
import { BaseColors } from "@config/theme";
import debounce from "lodash-es/debounce";
import isEmpty from "lodash-es/isEmpty";
import isString from "lodash-es/isString";
import MultipleImageView from "@components/MultipleImageView/MultipleImageView";
import RadioButton from "@components/CRadioButton";
import { useDispatch, useSelector } from "react-redux";
import Toast from "react-native-simple-toast";
import ProgressBarModal from "@components/ProgressBarModal";
import socketActions from "@redux/reducers/socket/actions";
import AuthActions from "@redux/reducers/auth/actions";
import { useFocusEffect } from "@react-navigation/native";
import CMultiDropdown from "@components/CDropDown/CMultiDropdown";
import PurChasePlanModal from "@components/PurchasePlanModal";
import { getRemainingPlanData } from "@screens/Payments/apiFunction";
import dayjs from "dayjs";
import { groupSearchMessage } from "@screens/Chat/apiCallFunction";
import { FontFamily } from "@config/typography";
import AddTagName from "@components/AddTagName";

const Video = require("react-native-video").default;
const Animated = require("react-native-reanimated").default;
const FadeIn = require("react-native-reanimated").FadeIn;
const FadeInDown = require("react-native-reanimated").FadeInDown;
const FadeInUp = require("react-native-reanimated").FadeInUp;

const CreatePostSchema = yup.object().shape({
  SelectedCountry: yup
    .array()
    .required("selectCountry")
    .test("is-empty", "selectCountry", (obj) => {
      return Object.keys(obj).length !== 0;
    }),
  SelectedState: yup
    .array()
    .required("selectState")
    .test("is-empty", "selectState", (obj) => {
      return Object.keys(obj).length !== 0;
    }),
  Audience: yup
    .array()
    .required("AudienceMsg")
    .test("is-empty", "AudienceMsg", (obj) => {
      return Object.keys(obj).length !== 0;
    }),
});

const withOutStateSchema = yup.object().shape({
  SelectedCountry: yup
    .array()
    .required("selectCountry")
    .test("is-empty", "selectCountry", (obj) => {
      return Object.keys(obj).length !== 0;
    }),

  Audience: yup
    .array()
    .required("AudienceMsg")
    .test("is-empty", "AudienceMsg", (obj) => {
      return Object.keys(obj).length !== 0;
    }),
});

const { setUploadPercentage } = socketActions;
const { setFromScreenName, setActivePlanData } = AuthActions;

const CreatePostForm = ({ navigation, route }) => {
  const dispatch = useDispatch();

  const srollRef = useRef(null);
  const HEIGHT = Dimensions.get("window").height;

  // selectedImage & cameraImg is only when we come from crete Post
  const selectedImage = route?.params?.images || [];
  const cameraImg = route?.params?.cameraImg || false;
  const DraftData = route?.params?.DraftData || [];

  // video is only when we come from crete reel
  const video = route?.params?.video || [];

  const { accessToken, isCurrentPlan, activePlanData } = useSelector(
    (state) => state.auth
  );

  const { uploadPercentage } = useSelector((uState) => uState?.socket);
  // State
  const [isLoading, setIsLoading] = useState(false);
  const [draftLoader, setDraftLoader] = useState(false);
  const [isKeyboardVisible, setKeyboardVisible] = useState(false);
  const [countryData, setCountryData] = useState([]);
  const [selectedCountryState, setSelectedCountryState] = useState([]);
  const [natureOfBusinessDadas, setNatureOfBusinessData] = useState([]);
  const [countryIds, setCountryIds] = useState([]);
  const [onChangeValue, setOnchangeValue] = useState(true);
  const [boostPostValueValue, setBoostPostValueValue] = useState(false);
  const [radioButtonValue, setRadioButtonValue] = useState(0);
  const [isPaymentModal, setIsPaymentModal] = useState(false);
  const [isCurrentActivePlan, setCurrentActivePlan] = useState({});
  const [searchGroupList, setSearchGroupList] = useState([]);
  const [selectedGroups, setSelectedGroups] = useState([]);
  const [textInputValue, setTextInputValue] = useState("");
  const [extraPadding, setExtraPadding] = useState(0);

  // Memo
  const isLoadingMemo = useMemo(() => isLoading, [isLoading]);
  const onChangeValueMemo = useMemo(() => onChangeValue, [onChangeValue]);
  const boostPostValueValueMemo = useMemo(
    () => boostPostValueValue,
    [boostPostValueValue]
  );
  const isKeyboardVisibleMemo = useMemo(
    () => isKeyboardVisible,
    [isKeyboardVisible]
  );

  // Memoize the callback to prevent unnecessary re-renders of AddTagName
  const handleSelectedGroups = useCallback((group) => {
    setSelectedGroups(group);
  }, []);

  const handleToRemainingPlan = async () => {
    const resp = await getRemainingPlanData();
    if (resp !== undefined && resp?.data?.success) {
      setCurrentActivePlan(resp?.data?.data);
      dispatch(setActivePlanData(resp?.data?.data));
    } else {
      Toast.show(
        resp?.data?.message || "Something went wrong please try again"
      );
    }
  };

  const {
    control,
    handleSubmit,
    setValue,
    clearErrors,
    getValues,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(
      countryIds?.indexOf(101) !== -1 ? CreatePostSchema : withOutStateSchema
    ),
  });
  const date = dayjs(isCurrentActivePlan?.plan_expire_date).unix();
  const currentDate = dayjs().unix();

  // function run when Draft and Share button is clicked
  const handleToDraft = async (type) => {
    console.log("🚀 ~ handleToDraft ~ type:", type);
    let e = getValues();
    const validation = draftValidation();
    if (validation) {
      setIsLoading(true);
      // uploadReel when we come from crete reel otherwise uploadPost API is call
      let uploadType =
        !isEmpty(selectedImage) || !isEmpty(cameraImg)
          ? "uploadPost"
          : "uploadReel";

      const resp = await onsubmit(
        {
          ...e,
          is_shareable: onChangeValueMemo,
          is_boost: boostPostValueValueMemo,
        },
        setIsLoading,
        accessToken,
        type,
        uploadType
      );

      if (resp?.data?.success) {
        setTimeout(() => {
          navigation.replace("HomeScreen");
          setIsLoading(false);
          setDraftLoader(false);
          dispatch(setUploadPercentage(0));
          Toast.show(resp?.data?.message || "Post added Draft successfully");
        }, 2000); // 2000 milliseconds = 2 seconds
      } else {
        setIsLoading(false);
        setDraftLoader(false);
        dispatch(setUploadPercentage(0));
        if (resp?.data?.message === "Network error") {
          if (type) {
            handleToDraft(type);
          } else {
            handleToDraft();
          }
        } else {
          Toast.show(resp?.data?.message || "Post failed to add");
        }
      }
    } else {
      setIsPaymentModal(true);
    }
  };

  const draftValidation = () => {
    if (activePlanData?.is_prime_user || activePlanData?.is_free_user) {
      return true;
    } else {
      if (
        (!isEmpty(isCurrentPlan) &&
          isCurrentActivePlan?.post_count !== 0 &&
          isCurrentActivePlan?.post_reels_count !== 0 &&
          date > currentDate) ||
        activePlanData?.is_prime_user ||
        activePlanData?.is_free_user
      ) {
        return true;
      } else {
        return false;
      }
    }
  };
  // it is going to check user post and reel available or not
  const isHandleToUpdateCondition = () => {
    let uploadType =
      !isEmpty(selectedImage) || !isEmpty(cameraImg)
        ? "uploadPost"
        : "uploadReel";
    if (
      activePlanData?.is_prime_user ||
      activePlanData?.is_free_user ||
      (uploadType === "uploadPost" &&
        activePlanData?.allow_unlimited_post === 1) ||
      (uploadType === "uploadReel" &&
        activePlanData?.allow_unlimited_reel === 1)
    ) {
      return true;
    } else {
      if (uploadType === "uploadPost") {
        if (
          (!isEmpty(activePlanData) &&
            activePlanData?.post_count > 0 &&
            date >= currentDate) ||
          activePlanData?.is_prime_user ||
          activePlanData?.is_free_user
        ) {
          return true;
        } else {
          return false;
        }
      } else {
        if (
          (!isEmpty(activePlanData) &&
            activePlanData?.post_reels_count > 0 &&
            date >= currentDate) ||
          activePlanData?.is_prime_user ||
          activePlanData?.is_free_user
        ) {
          return true;
        } else {
          return false;
        }
      }
    }
  };

  const handleToSubmit = async (e, type) => {
    const validation = isHandleToUpdateCondition();
    if (validation) {
      setIsLoading(true);
      // uploadReel  when we come from crete reel otherwise uploadPost API is call
      let uploadType =
        !isEmpty(selectedImage) || !isEmpty(cameraImg)
          ? "uploadPost"
          : "uploadReel";

      // Separate existing groups from new groups
      const existingGroups = selectedGroups.filter((g) => !g.isNewGroup);
      const newGroups = selectedGroups.filter((g) => g.isNewGroup);

      const groupIds = existingGroups.map((g) => g.group_details_id);
      const newGroupNames = newGroups.map((g) => g.groupName);

      const sendData = {
        ...e,
        group_details_ids: groupIds,
        new_group_names: newGroupNames, // Send new group names separately
        is_shareable: onChangeValueMemo,
        is_boost: boostPostValueValueMemo,
      };
      const resp = await onsubmit(
        sendData,
        setIsLoading,
        accessToken,
        boostPostValueValueMemo ? "draft" : type,
        uploadType
      );

      if (resp?.data?.success) {
        if (boostPostValueValueMemo) {
          setIsLoading(false);
          setDraftLoader(false);
          dispatch(setUploadPercentage(0));
          navigation.navigate("CreateBoostPost", {
            data: {
              ...e,
              is_shareable: onChangeValueMemo,
              is_boost: boostPostValueValueMemo,
            },
            countryData: countryData,
            stateData: selectedCountryState,
            natureOfBusinessDada: natureOfBusinessDadas,
            type:
              !isEmpty(selectedImage) || !isEmpty(cameraImg) ? "post" : "reel",
            apiRes: {
              ...resp?.data?.data,

              post_id: resp?.data?.data?.post_id || DraftData?.post_id,
              reel_id: resp?.data?.data?.reel_id || DraftData?.reel_id,
            },
          });
        } else {
          setTimeout(() => {
            dispatch(setFromScreenName("createPostScreen"));
            navigation.navigate("Home", {
              screen: "HomeTab",
              params: {},
            });
            setIsLoading(false);
            setDraftLoader(false);
            dispatch(setUploadPercentage(0));
            if (type === "Draft") {
              Toast.show(
                resp?.data?.message || "Post Draft added successfully"
              );
            } else {
              Toast.show(resp?.data?.message || "Post added successfully");
            }
          }, 2000);
        }
      } else {
        setIsLoading(false);
        setDraftLoader(false);
        dispatch(setUploadPercentage(0));
        if (resp?.data?.message === "Network error") {
          if (type) {
            handleToSubmit(e, type);
          }
        } else {
          Toast.show(resp?.data?.message || "Post failed to add");
        }
      }
    } else {
      setIsPaymentModal(true);
    }
  };

  // useEffect is run when keyboard is open
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      "keyboardDidShow",
      () => {
        setKeyboardVisible(true);
      }
    );

    const keyboardDidHideListener = Keyboard.addListener(
      "keyboardDidHide",
      debounce(() => {
        setKeyboardVisible(false);
      }, 200)
    );

    // Clean up the listeners when the component unmounts
    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
      debouncedSearch.cancel(); // Cancel any pending debounced calls
    };
  }, [debouncedSearch]);

  // Fetch Country data and manipulate response
  const fetchCountryData = async (e) => {
    const resp = await getCountryData(e, setIsLoading);
    let updatedData = resp?.map((val) => {
      return {
        ...val,
        label: val.name,
        value: val.id,
      };
    });
    if (!isEmpty(DraftData)) {
      // If DraftData is not empty
      const arrayData = DraftData?.country?.split(",")?.map(Number);
      setValue("SelectedCountry", arrayData || []);
      setCountryIds(arrayData || []);
    } else {
      const ids = resp.map((country) => country.id);
      if (!isEmpty(ids)) {
        setValue("SelectedCountry", ids);
        setCountryIds(ids);
      }
    }
    setCountryData(updatedData);
  };

  useFocusEffect(
    useCallback(() => {
      fetchCountryData();
      fetchNatureOfBusinessData();
      fetchCountryStateData(101);
      handleToRemainingPlan();
      if (!isEmpty(selectedImage) || !isEmpty(cameraImg)) {
        setValue("files", selectedImage);
      } else {
        setValue("files", video);
      }
    }, [])
  );

  useFocusEffect(
    useCallback(() => {
      if (!isEmpty(DraftData)) {
        setValue("PostDescription", DraftData?.description);
        setOnchangeValue(Number(DraftData?.is_shareable) ? true : false);
        setBoostPostValueValue(Number(DraftData?.is_boost) ? true : false);
        setValue("priority", Number(DraftData?.is_private || 0));
        setValue("post_id", DraftData?.post_id || DraftData?.reel_id);
        setRadioButtonValue(Number(DraftData?.is_private));
      }
    }, [])
  );

  // fetch Country State Data and manipulate response
  const fetchCountryStateData = async (id) => {
    const resp = await getCountryStateData(id);
    let updatedData = resp?.map((val) => {
      return {
        ...val,
        label: val.name,
        value: val.id,
      };
    });
    if (!isEmpty(DraftData)) {
      if (DraftData?.state && !isEmpty(DraftData?.state)) {
        const arrayData = DraftData?.state?.split(",")?.map(Number);
        setValue("SelectedState", arrayData);
      }
    } else {
      const ids = resp.map((country) => country.id);
      if (!isEmpty(ids)) {
        setValue("SelectedState", ids);
      }
    }
    setSelectedCountryState(updatedData);
  };

  // fetch Nature Of Business Data and manipulate response
  const fetchNatureOfBusinessData = async () => {
    const resp = await natureOfBusinessData();
    let updatedData = resp?.map((val) => {
      return {
        ...val,
        value: val.id,
      };
    });
    if (!isEmpty(DraftData)) {
      // if DraftData is not empty
      const arrayData = DraftData?.audience?.split(",")?.map(Number);
      setValue("Audience", arrayData);
    } else {
      const audienceIDs = resp?.map((val) => val.id);
      setValue("Audience", audienceIDs);
    }

    setNatureOfBusinessData(updatedData);
  };

  const handleToSearchText = async (searchBtnData) => {
    // If search text is empty, clear the search results
    if (!searchBtnData?.trim()) {
      setSearchGroupList([]);
      return;
    }

    const resp = await groupSearchMessage(searchBtnData?.trim(), 1);

    if (resp?.data?.success && !isEmpty(resp?.data?.data)) {
      setSearchGroupList({
        data: resp?.data?.data,
      });
    } else {
      // When API returns empty array, show the search text as an option to add
      setSearchGroupList({
        data: [],
        showAddOption: true,
        searchText: searchBtnData?.trim(),
      });
    }
  };

  // Create a debounced version of the search function
  const debouncedSearch = useCallback(
    debounce((text) => {
      handleToSearchText(text);
    }, 300),
    []
  );

  return (
    <SafeAreaView style={styles.mainView}>
      <CHeader
        handleBackButton={
          !isEmpty(DraftData)
            ? () => navigation.goBack()
            : !isEmpty(selectedImage) || !isEmpty(cameraImg)
              ? () =>
                  navigation.navigate("AddPostScreen", { cameraImg: cameraImg })
              : () =>
                  navigation.navigate("AddReelScreen", { cameraImg: cameraImg })
        }
        headingTitle={
          !isEmpty(selectedImage) || !isEmpty(cameraImg)
            ? "createPost"
            : "createReel"
        }
        CancelTxt="Cancel"
        headerTitleStyle={{ fontSize: 20 }}
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={{
          flex: 1,
          backgroundColor: BaseColors.white,
        }}
        enabled
      >
        <ScrollView
          ref={srollRef}
          contentContainerStyle={{ flexGrow: 1, paddingTop: extraPadding }}
          keyboardShouldPersistTaps="handled"
        >
          {!isEmpty(selectedImage) ? (
            <MultipleImageView
              selectedImage={selectedImage}
              addMoreBtn={false}
              showDots={selectedImage.length > 1 ? true : false}
            />
          ) : !isEmpty(video) ? (
            <Video
              source={{ uri: `${video[0]?.uri || video?.fileUrl}` }}
              style={{
                height: HEIGHT / 2.5,
                backgroundColor: BaseColors.lightGray,
              }}
              controls={false}
              repeat
              resizeMode={"contain"}
            />
          ) : null}

          <View style={styles.contentView}>
            <View
              style={[
                styles.mainInputStyle,
                {
                  height: 80,
                  backgroundColor: "#FBFBFB",
                  justifyContent: "center",
                  alignItems: "center",
                  borderRadius: 10,
                  borderWidth: 1,
                  borderColor: "#EBEAEA",
                },
              ]}
            >
              <Controller
                control={control}
                rules={{
                  required: true,
                }}
                render={({ field: { onChange, value } }) => {
                  return (
                    <RadioButton
                      options={[
                        { label: "Public", value: "Public" },
                        { label: "Private", value: "Private" },
                      ]}
                      selectedOption1={(e, id) => {
                        setValue("priority", e);
                        setRadioButtonValue(id);
                        if (id === 1) {
                          setBoostPostValueValue(false);
                        }
                      }}
                      defaultData={radioButtonValue}
                      type={"dot"}
                    />
                  );
                }}
                name="priority"
              />
            </View>
            <Animated.View entering={FadeInDown} style={styles.cInputStyle}>
              <Controller
                control={control}
                rules={{
                  required: false,
                }}
                name="PostDescription"
                render={({ field: { onChange, value } }) => (
                  <CInput
                    placeholderText={
                      !isEmpty(selectedImage) || !isEmpty(cameraImg)
                        ? translate("PostDescription")
                        : translate("ReelDescription")
                    }
                    rightIconSize={18}
                    returnKeyType="done"
                    value={value}
                    onChange={onChange}
                    maxLength={255}
                    onSubmit={() => {
                      Keyboard.dismiss();
                    }}
                    isError={errors?.PostDescription}
                    isErrorMsg={errors?.PostDescription?.message}
                    multiline
                    numberOfLines={5}
                  />
                )}
              />
            </Animated.View>
            {radioButtonValue === 0 && (
              <View style={{ zIndex: 10 }}>
                <Controller
                  control={control}
                  rules={{
                    required: true,
                  }}
                  name="group_details_ids"
                  render={({ field: { onChange, value } }) => {
                    return (
                      <Animated.View
                        entering={FadeInDown}
                        style={styles.cInputStyle}
                      >
                        <AddTagName
                          placeholder={translate("Add #tags")}
                          listPosition="top"
                          onselectedGrop={handleSelectedGroups}
                          navigation={navigation}
                          srollRef={srollRef}
                          setExtraPadding={setExtraPadding}
                        />
                      </Animated.View>
                    );
                  }}
                />
              </View>
            )}

            <View style={{ zIndex: 10 }}>
              <Controller
                control={control}
                rules={{
                  required: true,
                }}
                name="SelectedCountry"
                render={({ field: { onChange, value } }) => {
                  return (
                    <Animated.View
                      entering={FadeInDown}
                      style={styles.cInputStyle}
                    >
                      <CMultiDropdown
                        showText="showTextshowText"
                        data={countryData}
                        required
                        multiple
                        placeholder="SelectCountry"
                        value={isString(value) ? value.split(",") : value || []}
                        valueProp="indication_id"
                        listProps="name"
                        setItem={(e) => {
                          onChange(e);
                          setCountryIds(e);

                          if (e?.indexOf(101) !== -1) {
                            clearErrors("SelectedState");
                          }
                        }}
                        listPosition="top"
                        setValue={setValue}
                        dropDownName={"SelectedCountry"}
                        showError={errors?.SelectedCountry}
                        errorMsg={errors?.SelectedCountry?.message}
                        setCountryIds={setCountryIds}
                      />
                    </Animated.View>
                  );
                }}
              />
            </View>

            {countryIds?.indexOf(101) !== -1 ? (
              <View>
                <Controller
                  control={control}
                  rules={{
                    required: true,
                  }}
                  name="SelectedState"
                  render={({ field: { onChange, value } }) => {
                    return (
                      <Animated.View
                        entering={FadeInDown}
                        style={styles.mainInputStyle}
                      >
                        <CMultiDropdown
                          showText={translate("SelectState")}
                          data={selectedCountryState}
                          required
                          multiple
                          placeholder="SelectState"
                          value={value}
                          valueProp="indication_id"
                          listProps="name"
                          setItem={onChange}
                          listPosition="top"
                          setValue={setValue}
                          dropDownName={"SelectedState"}
                          showError={errors?.SelectedState}
                          errorMsg={errors?.SelectedState?.message}
                        />
                      </Animated.View>
                    );
                  }}
                />
              </View>
            ) : null}
            <View>
              <Controller
                control={control}
                rules={{
                  required: true,
                }}
                name="Audience"
                render={({ field: { onChange, value } }) => (
                  <Animated.View
                    entering={FadeInDown}
                    style={styles.mainInputStyle}
                  >
                    <CMultiDropdown
                      showText={translate("Audience")}
                      data={natureOfBusinessDadas}
                      required
                      multiple
                      placeholder="Audience"
                      value={value}
                      valueProp="indication_id"
                      listProps="name"
                      setItem={onChange}
                      listPosition="top"
                      setValue={setValue}
                      dropDownName={"Audience"}
                      showError={errors?.Audience}
                      errorMsg={errors?.Audience?.message}
                    />
                  </Animated.View>
                )}
              />
            </View>

            <View style={styles.sharedTextView}>
              <Text style={styles.textView}>{translate("sharedText")}</Text>
              <View
                style={[
                  styles.switchCardView,
                  {
                    backgroundColor: "#EFEFEF",
                  },
                ]}
              >
                <Switch
                  trackColor={{
                    false: "transparent",
                    true: "transparent",
                  }}
                  thumbColor={onChangeValue ? BaseColors.activeTab : "white"}
                  ios_backgroundColor="transparent"
                  onValueChange={() => setOnchangeValue(!onChangeValue)}
                  value={onChangeValue}
                />
              </View>
            </View>

            <Animated.View
              style={styles.sharedTextView}
              entering={FadeInDown}
              exiting={FadeInUp}
            >
              <Text style={styles.textView}>
                {!isEmpty(selectedImage) || !isEmpty(cameraImg)
                  ? translate("boostText")
                  : translate("boostReelTextForRadio")}
              </Text>
              <View
                style={[
                  styles.switchCardView,
                  {
                    backgroundColor: "#EFEFEF",
                  },
                ]}
              >
                <Switch
                  trackColor={{
                    false: "transparent",
                    true: "transparent",
                  }}
                  thumbColor={
                    boostPostValueValueMemo ? BaseColors.activeTab : "white"
                  }
                  ios_backgroundColor="transparent"
                  onValueChange={(e) => {
                    console.log("Your Design is :", e);
                    setBoostPostValueValue(!boostPostValueValueMemo);
                    if (e === true) {
                      setRadioButtonValue(0);
                    }
                  }}
                  value={boostPostValueValueMemo}
                />
              </View>
            </Animated.View>
          </View>
        </ScrollView>
        {isKeyboardVisibleMemo ? null : (
          <Animated.View
            style={[
              styles.mainButton,
              {
                flexDirection: "row",
                justifyContent: "space-between",
                paddingHorizontal: 20,
              },
            ]}
            entering={FadeIn}
          >
            {!isEmpty(DraftData) ? (
              <CButton
                style={{ width: "100%" }}
                onBtnClick={handleSubmit((e) => handleToSubmit(e, "Share"))}
              >
                {translate("Share")}
              </CButton>
            ) : (
              <>
                <CButton
                  type="outlined"
                  style={{ width: "48.5%" }}
                  bgStyle={{ borderColor: "#282C36" }}
                  onBtnClick={() => handleToDraft("Draft")}
                  blackBtn={true}
                  txtSty={{ color: BaseColors.black }}
                  loading={draftLoader}
                >
                  {translate("Draft")}
                </CButton>
                <CButton
                  style={{ width: "48.5%" }}
                  onBtnClick={handleSubmit((e) => handleToSubmit(e, "Share"))}
                >
                  {translate("Share")}
                </CButton>
              </>
            )}
          </Animated.View>
        )}
      </KeyboardAvoidingView>

      {isLoadingMemo ? (
        <ProgressBarModal
          visible={isLoadingMemo}
          progress={uploadPercentage / 100}
        />
      ) : null}

      <PurChasePlanModal
        visible={isPaymentModal}
        setModalVisible={(e) => setIsPaymentModal(e)}
        text={"currentlyPlanText"}
        navigation={navigation}
      />
    </SafeAreaView>
  );
};
export default memo(CreatePostForm);
