import { useCallback, useMemo, useRef, useState } from "react";
import {
  Dimensions,
  Platform,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
// import Video from "react-native-video";
import styles from "./styles";
import { CustomIcon } from "@config/LoadIcons";
// import FastImage from "react-native-fast-image";
import { BaseColors } from "@config/theme";
// import LottieView from "lottie-react-native";
// import { images } from "@config/images";
import truncate from "lodash-es/truncate";
import { translate } from "../../lang/Translate";
import Toast from "react-native-simple-toast";
import { navigationRef } from "@navigation/NavigationService";
import { FontFamily } from "@config/typography";

const { images } = require("@config/images");
const LottieView = require("lottie-react-native").default;
const Video = require("react-native-video").default;
const FastImage = require("react-native-fast-image");
// For Render Reel Item Component
export const RenderItem = ({
  item,
  index,
  currentVideoIndex = 0,
  isLikeAnim,
  type,
  setIsLikeAnim = () => {},
  setIsShareListModal = () => {},
  handleToFollow = () => {},
  setModalInfoModal = () => {},
  handleToLike = () => {},
  onDoubleTap = () => {},
  handleToComment = () => {},
  handleToAction = () => {},
  handleToLikeCount = () => {},
  navigation,
  userData,
  videoRefs,
  handleToPressBoost = () => {},
}) => {
  // Expand State's
  const [expandedIndex, setExpandedIndex] = useState(null);
  const [orientation, setOrientation] = useState("portrait");
  const [isLoadVideo, setIsLoadVideo] = useState(false);

  // Expand State's Memo
  const expandedIndexMemo = useMemo(() => expandedIndex, [expandedIndex]);
  const orientationMemo = useMemo(() => orientation, [orientation]);
  const isLoadVideoMemo = useMemo(() => isLoadVideo, [isLoadVideo]);

  // last Ref Reference
  const lastTapRef = useRef(0);
  // For Double Tap Function
  const handleDoubleTap = () => {
    const now = Date.now();
    if (now - lastTapRef.current < 300) {
      onDoubleTap();
    }
    lastTapRef.current = now;
  };
  // Expand Description Function
  const toggleExpand = useCallback(
    (index) => {
      setExpandedIndex(expandedIndexMemo === index ? null : index);
    },
    [expandedIndexMemo]
  );

  const preloadNextVideo = useCallback(() => {
    if (currentVideoIndex + 1 > -1) {
      const ref = videoRefs?.current[currentVideoIndex + 1]
        ? videoRefs?.current[currentVideoIndex + 1]
        : null;
      if (ref?.seek) {
        try {
          ref.seek(0); // triggers preload
        } catch (e) {
          console.log("Preload error:", e);
        }
      }
    }
  }, [videoRefs, currentVideoIndex]);

  return (
    // RenderItem MainView
    <TouchableOpacity style={styles.renderDataMainView} activeOpacity={1}>
      <View style={{ ...styles.videoView }}>
        {/* {item?.ReelData?.thumbnailData?.thumbUrl ? (
          <View
            style={{
              position: "absolute",
              top: 0,
              bottom: 0,
              left: 0,
              right: 0,
              opacity: isLoadVideoMemo ? 0 : 1,
              justifyContent: "center",
            }}
          >
            <FastImage
              source={{ uri: item?.ReelData?.thumbnailData?.thumbUrl }}
              style={{
                width: Dimensions.get("screen").width,
                height: Dimensions.get("screen").height,
              }}
              resizeMode="contain"
            />
          </View>
        ) : null} */}
        {index === currentVideoIndex + 1 && (
          <Video
            ref={videoRefs.current[currentVideoIndex + 1]}
            source={{ uri: item?.ReelData?.fileUrl }}
            paused={true}
            resizeMode="cover"
            onLoad={() => {
              console.log("Next video preloaded!", item?.ReelData?.fileUrl);
            }}
            playInBackground={false}
            playWhenInactive={false}
            style={{
              width: 1,
              height: 1,
              opacity: 0,
            }}
          />
        )}
        {item?.ReelData?.fileUrl &&
        navigationRef.current.getCurrentRoute().name === "reels" ? (
          <Video
            ref={videoRefs.current[currentVideoIndex]}
            source={{
              uri: item?.ReelData?.fileUrl,
            }}
            poster={item?.ReelData?.thumbnailData?.thumbUrl}
            posterResizeMode={"contain"}
            automaticallyWaitsToMinimizeStalling={true}
            minLoadRetryCount={6}
            resizeMode={orientationMemo === "landscape" ? "contain" : "contain"}
            paused={currentVideoIndex !== index}
            onLoadStart={(r) => setIsLoadVideo(false)}
            onLoad={(e) => {
              preloadNextVideo(currentVideoIndex + 1);
              setIsLoadVideo(true);
              setOrientation(e?.naturalSize?.orientation);
            }}
            style={styles.videoStyle}
            repeat={true}
            hideShutterView={true}
            bufferConfig={{
              minBufferMs: 15000,
              maxBufferMs: 50000,
              bufferForPlaybackMs: 2500,
              bufferForPlaybackAfterRebufferMs: 5000,
            }}
            // onError={() => console.log("Failed To Load")}
          />
        ) : null}
      </View>

      {/* Render User Detail Information */}
      <View
        style={[
          styles.bottomView,
          {
            bottom:
              type === "fromSavedScreen"
                ? 0
                : Platform.OS === "ios" && Dimensions.get("window").height < 800
                  ? 20
                  : 95,
          },
        ]}
      >
        <TouchableOpacity
          style={styles.contentView}
          activeOpacity={0.9}
          onPress={() => {
            if (item?.user_data[0]?.user_id !== userData?.user_id) {
              navigation.navigate("ProfileNew", {
                data: item?.user_data[0],
                type: "anotherProfile",
              });
            } else {
              navigation.navigate("ProfileNew", {
                data: item?.user_data[0],
              });
            }
          }}
        >
          <FastImage
            source={{ uri: item?.user_data[0]?.user_dp }}
            style={styles.profileDPStyle}
          />
          <Text style={styles.userNameStyle}>
            {item?.user_data[0]?.username}
          </Text>
          {item?.is_self === false ? (
            <TouchableOpacity
              style={styles.btnViewStyle}
              onPress={() => {
                handleToFollow(item?.user_data[0]?.user_id, "add");
              }}
              activeOpacity={0.8}
            >
              <Text style={styles.btnTextStyle}>
                {!item?.is_followed ? translate("followText") : "Following"}
              </Text>
            </TouchableOpacity>
          ) : null}
          {userData?.user_id === item?.user_data[0]?.user_id &&
          item.is_boost === 0 ? (
            <TouchableOpacity
              activeOpacity={0.9}
              style={styles.boostBtnView}
              onPress={() => handleToPressBoost(item, navigation)}
            >
              <Text style={styles.boostBtnText}>
                {translate("boostReelText")}
              </Text>
            </TouchableOpacity>
          ) : null}
        </TouchableOpacity>

        {item?.is_boost === 1 ? (
          <TouchableOpacity
            style={styles.boostReelView}
            activeOpacity={0.8}
            onPress={() =>
              handleToAction(
                item?.action,
                item?.user_data[0],
                item?.conversation_id,
                item
              )
            }
          >
            <Text style={styles.boostTextStyle}>
              {item?.user_data[0]?.user_id !== userData?.user_id
                ? item?.action === "contact_now"
                  ? "Contact Now"
                  : item?.action === "send_message"
                    ? "Send Message"
                    : item?.action === "visit_my_profile"
                      ? "Visit My Profile"
                      : null
                : "View insights"}
            </Text>

            <CustomIcon
              name="BsChevronRight"
              size={16}
              color={BaseColors.white}
            />
          </TouchableOpacity>
        ) : null}
        {item?.description && (
          <TouchableOpacity
            activeOpacity={0.9}
            onPress={() => toggleExpand(index)}
            style={styles.descMainViewStyle}
          >
            <Text
              style={[
                styles.descTextStyle,
                { maxWidth: expandedIndex === index ? "90%" : null },
              ]}
            >
              {expandedIndex === index
                ? item?.description
                : truncate(item?.description, { length: 35, omission: "..." })}
              {item?.description?.length > 35 ? (
                <Text style={styles.moreLessTextStyle}>
                  {expandedIndex === index ? " Less" : " More"}
                </Text>
              ) : null}
            </Text>
          </TouchableOpacity>
        )}
        {item?.group_names ? (
          <View
            style={{
              flexDirection: "row",
              flexWrap: "wrap",
              padding: 10,
              shadowColor: "#000",
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.25,
              shadowRadius: 3.84,
              elevation: 5,
            }}
          >
            {item?.group_names.map((li) => {
              return (
                <TouchableOpacity
                  onPress={() => {
                    navigation.navigate("Chat", {
                      screen: "chatTab",
                      params: { search: li },
                    });
                  }}
                  activeOpacity={0.7}
                >
                  <Text
                    style={[
                      {
                        color: BaseColors.White,
                        fontFamily: FontFamily.RobotoRegular,
                        fontSize: 16,
                      },
                    ]}
                  >
                    {li}{" "}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </View>
        ) : null}
        {item?.is_liked_my_follower ? (
          <Text
            style={[
              styles.descTextStyle,
              {
                fontSize: 12,
                marginTop: 10,
                marginLeft: 4,
                fontFamily: FontFamily.RobotoMedium,
              },
            ]}
          >
            Liked By {item?.engaged_followers?.username} and{" "}
            {Number(item?.like_counts) - 1} others
          </Text>
        ) : item?.engaged_my_followers ? (
          <Text
            style={[
              styles.descTextStyle,
              {
                fontSize: 12,
                marginTop: 10,
                marginLeft: 4,
                fontFamily: FontFamily.RobotoMedium,
              },
            ]}
          >
            Followed By {item?.engaged_followers?.username} and other
          </Text>
        ) : null}
      </View>
      {/* Social Media Method's  */}
      <View
        style={[
          styles.socialIconStyle,
          {
            bottom:
              type === "fromSavedScreen"
                ? 0
                : Dimensions.get("window").height < 800 && Platform.OS === "ios"
                  ? 0
                  : 50,
          },
        ]}
      >
        {/* Like Design Render With Animation */}
        <View style={styles.socialIconViewStyle}>
          <TouchableOpacity
            onPress={() => handleToLike(item?.reel_id, index)}
            activeOpacity={0.8}
          >
            {item?.is_liked ? (
              <>
                {isLikeAnim && index === currentVideoIndex ? (
                  <LottieView
                    autoSize={true}
                    source={images.like}
                    autoPlay={true}
                    loop={false}
                    style={styles.likeView}
                    onAnimationFinish={() => setIsLikeAnim(false)}
                  />
                ) : null}
                <CustomIcon
                  name="BsSuitHeartFill"
                  size={28}
                  color={BaseColors.activeTab}
                />
              </>
            ) : (
              <CustomIcon
                name="heart-outline"
                size={28}
                color={BaseColors.white}
              />
            )}
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => handleToLikeCount(item?.reel_id)}
            activeOpacity={0.8}
          >
            <Text style={styles.countTextStyle}>{item?.like_counts || 0}</Text>
          </TouchableOpacity>
        </View>
        {/* Render Comment Design */}
        <TouchableOpacity
          style={styles.socialIconViewStyle}
          onPress={() => handleToComment(item?.reel_id)}
          activeOpacity={0.8}
        >
          <CustomIcon name="BsChat" size={28} color={BaseColors.white} />
          <Text style={styles.countTextStyle}>{item?.comment_counts}</Text>
        </TouchableOpacity>
        {/* Render Share Design */}
        <TouchableOpacity
          style={styles.socialIconViewStyle}
          onPress={() => setIsShareListModal(true)}
          activeOpacity={0.8}
        >
          <CustomIcon
            name="share-3---Copy"
            size={28}
            color={BaseColors.white}
          />
        </TouchableOpacity>
        {/* Render More info Design */}
        <TouchableOpacity
          style={styles.socialIconViewStyle}
          onPress={() => setModalInfoModal({ modalVisible: true, data: item })}
          activeOpacity={0.8}
        >
          <CustomIcon name="dot" size={28} color={BaseColors.white} />
        </TouchableOpacity>
      </View>
    </TouchableOpacity>
  );
};
