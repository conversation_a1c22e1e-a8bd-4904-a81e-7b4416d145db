import React, { memo, useMemo, useRef, useState } from "react";
import {
  Image,
  KeyboardAvoidingView,
  SafeAreaView,
  ScrollView,
  Text,
  View,
} from "react-native";
import styles from "./styles";
import { images } from "@config/images";
import CInput from "@components/TextInput";
import CButton from "@components/CButton";
import CHeader from "@components/CHeader";
import { translate } from "../../lang/Translate";
import * as yup from "yup";
import { Controller, useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { onsubmit } from "./apiFunctions";
import { BaseColors } from "@config/theme";
import Toast from "react-native-simple-toast";

const NewPasswordSchema = yup.object().shape({
  new_password: yup
    .string()
    .required("enterPsd")
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&_])[A-Za-z\d@$!%*?&_]{8,}$/,
      "PasswordsMustBe"
    ),
  reEnter_password: yup
    .string()
    .required("reEnterPsd")
    .oneOf([yup.ref("new_password"), null], "passwordsMatch"),
});

const CNewPassWordAfterForgot = ({ navigation, route }) => {
  // ref's
  const newPassRef = useRef(null);
  const reenterPassRef = useRef(null);
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(NewPasswordSchema),
  });

  const [isLoading, setIsLoading] = useState(false);

  const isLoadingMemo = useMemo(() => isLoading, [isLoading]);
  const handleToSubmit = async (e) => {
    setIsLoading(true);
    const resp = await onsubmit(e, route?.params?.data, navigation);

    if (resp?.data?.success) {
      navigation.navigate("EmailWithLogin", {
        loginType: route?.params?.data?.type === "email" ? "email" : "mobile",
      });
    } else {
      Toast.show(resp?.data?.message);
    }
  };

  return (
    <SafeAreaView style={styles.mainView}>
      <CHeader
        handleBackButton={() =>
          navigation.navigate("CForgotPasswordEmail", {
            loginType:
              route?.params?.data?.type === "email" ? "email" : "mobile",
          })
        }
      />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : null}
        style={{
          flex: 1,
          backgroundColor: BaseColors.white,
        }}
      >
        <ScrollView>
          <Image
            source={images.footMainLogo}
            style={styles.mainFootLogo}
            resizeMode={"contain"}
          />
          <View style={styles.contentView}>
            <Text style={styles.headingText}>
              {translate("forgotPasswordText")}?
            </Text>
            <Text style={styles.descText}>{translate("enterPassText")}</Text>

            <View style={styles.cInputStyle}>
              <Controller
                control={control}
                rules={{
                  required: true,
                }}
                name="new_password"
                render={({ field: { onChange, value } }) => (
                  <CInput
                    reference={newPassRef}
                    placeholderText="Enter New Password"
                    rightIconSize={18}
                    returnKeyType="next"
                    value={value}
                    onChange={onChange}
                    onSubmit={() => {
                      reenterPassRef.current.focus();
                    }}
                    passwordInputField={true}
                    isError={errors?.new_password}
                    isErrorMsg={errors?.new_password?.message}
                  />
                )}
              />
            </View>
            <View style={styles.cInputStyle}>
              <Controller
                control={control}
                rules={{
                  required: true,
                }}
                name="reEnter_password"
                render={({ field: { onChange, value } }) => (
                  <CInput
                    reference={reenterPassRef}
                    placeholderText="Confirm Password"
                    passwordInputField={true}
                    returnKeyType="next"
                    value={value}
                    onChange={onChange}
                    onSubmit={handleSubmit(handleToSubmit)}
                    isError={errors?.reEnter_password}
                    isErrorMsg={errors?.reEnter_password?.message}
                  />
                )}
              />
            </View>
            <CButton
              style={styles.btnStyle}
              onBtnClick={handleSubmit((e) => handleToSubmit(e))}
              loading={isLoadingMemo}
            >
              {translate("confirmText")}
            </CButton>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};
export default memo(CNewPassWordAfterForgot);
