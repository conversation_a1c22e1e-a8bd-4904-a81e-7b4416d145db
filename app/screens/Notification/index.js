import React, { useCallback, useEffect, useRef, useState } from "react";
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Platform,
  SafeAreaView,
  ScrollView,
  RefreshControl,
} from "react-native";
import styles from "./styles";
import { isEmpty, isNumber, startCase, truncate } from "lodash-es";
import { BaseColors } from "@config/theme";
import FastImage from "react-native-fast-image";
import { CustomIcon } from "@config/LoadIcons";
import { Swipeable } from "react-native-gesture-handler";
import { useFocusEffect } from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import ModalComponent from "@components/Modal";
import NoRecord from "@components/NoRecord";
import CHeader from "@components/CHeader";
import MiniLoader from "@components/MiniLoader";
import authActions from "@redux/reducers/auth/actions";
import Toast from "react-native-simple-toast";
import {
  deleteNotification,
  getNotificationList,
  readNotificationAPI,
} from "./apiCallFunction";

const {
  setNotificationData,
  setSavedPostList,
  setSavedReelList,
  setNotificationActivity,
  setNotificationCount,
} = authActions;

const NotificationScreen = ({ navigation }) => {
  const swipeableRef = useRef(null);

  const dispatch = useDispatch();

  // state
  const [visible, setModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [bottomLoading, setBottomLoading] = useState(false);
  const [selectedNotification, setSelectedNotification] = useState("");
  const [modelButtonLoading, setModelButtonLoading] = useState(false);

  // redux State
  const { notificationData, notificationActivity } = useSelector(
    (state) => state.auth
  );

  // fetch notification List
  useFocusEffect(
    useCallback(() => {
      fetchNotificationData();
      dispatch(setNotificationCount(0));
    }, [])
  );

  // fetch notification Data when notification come in socket
  useEffect(() => {
    if (!isEmpty(notificationActivity)) {
      fetchNotificationData();
    }
  }, [notificationActivity]);

  // remove selected dot
  useEffect(() => {
    if (!visible) {
      setSelectedNotification("");
    }
  }, [visible]);

  // Fetch Notification Data API
  const fetchNotificationData = async (page = 1, bottomLoader = false) => {
    if (isEmpty(notificationData?.data)) {
      setLoading(true);
    }
    if (bottomLoader) {
      setBottomLoading(true);
    }
    const resp = await getNotificationList(page);

    setLoading(false);
    if (!isEmpty(resp?.data?.data) && resp?.data?.data !== undefined) {
      dispatch(setNotificationActivity(""));
      dispatch(
        setNotificationData({
          page: page,
          next_enable: resp?.data?.pagination?.hasNextPage,
          data:
            page > 1
              ? [...notificationData?.data, ...resp?.data?.data]
              : resp?.data?.data,
        })
      );

      setLoading(false);
      setBottomLoading(false);
    } else {
      dispatch(setNotificationActivity(""));
      dispatch(setNotificationData([]));
      setBottomLoading(false);
      setLoading(false);

      Toast.show(resp?.data?.message || "No Data Found");
    }
  };

  // clear all notification list API
  const handleClearAllNotification = async () => {
    setModelButtonLoading(true);
    const resp = await deleteNotification();
    if (resp?.data?.success) {
      dispatch(setNotificationData([]));
      setModelButtonLoading(false);
      setModalVisible(false);
      console.log(resp?.data?.message || "Successfully");
    } else {
      setModelButtonLoading(false);
      setModalVisible(false);
      Toast.show(resp?.data?.message || "No Data Found");
    }
  };

  // delete single notification API
  const deleteSingleNotification = async () => {
    setModelButtonLoading(true);

    const resp = await deleteNotification(selectedNotification);
    if (resp?.data?.success) {
      const updatedArray = notificationData?.data?.map((dayData) => {
        return {
          notifications: {
            day: dayData?.notifications?.day,
            data: dayData?.notifications?.data?.filter(
              (notification) =>
                notification?.notification_id !== selectedNotification
            ),
          },
        };
      });

      if (
        updatedArray?.every(
          (dayData) => dayData?.notifications?.data?.length === 0
        )
      ) {
        dispatch(setNotificationData([])); // Set to empty array if all data is empty
      } else {
        dispatch(
          setNotificationData({ ...notificationData, data: updatedArray })
        );
      }
      setModelButtonLoading(false);
      setModalVisible(false);
    } else {
      setModelButtonLoading(false);
      setModalVisible(false);
      Toast.show(resp?.data?.message || "No Data Found");
    }
  };

  const handleSingleDelete = (NotificationId) => {
    setSelectedNotification(NotificationId);
    setModalVisible(true);
  };

  const handleReadComment = async (selectedNotificationID) => {
    const resp = await readNotificationAPI(selectedNotificationID);
    if (resp?.data?.success) {
      // Map through the data and update is_read
      const updatedData = notificationData?.data?.map((item) => ({
        ...item,
        notifications: {
          ...item?.notifications,
          data: item?.notifications?.data?.map((notification) => {
            if (notification.notification_id === selectedNotificationID) {
              // Update is_read to 1
              return { ...notification, read: 1 };
            } else {
              return notification;
            }
          }),
        },
      }));
      dispatch(setNotificationData({ ...notificationData, data: updatedData }));
    } else {
      Toast.show(resp?.data?.message || "No Data Found");
    }
  };

  // handle navigation when click on notification
  const handleNavigation = (selectedItemData) => {
    handleReadComment(selectedItemData?.notification_id);
    if (selectedItemData?.message?.meta?.notificationType === "post") {
      if (selectedItemData?.postData && !isEmpty(selectedItemData?.postData)) {
        const updatedPostList = {
          page: 1,
          next_enable: false,
          data: [selectedItemData?.postData],
        };
        dispatch(setSavedPostList(updatedPostList));
        navigation.navigate("SavedPostList", {
          data: [selectedItemData?.postData],
          screenName: "notification",
          notificationType: "post",
        });
      }
    } else if (
      selectedItemData?.message?.meta?.notificationType === "comment"
    ) {
      if (
        selectedItemData?.commentData &&
        !isEmpty(selectedItemData?.commentData)
      ) {
        const updatedPostList = {
          page: 1,
          next_enable: false,
          data: [selectedItemData?.commentData],
        };
        if (selectedItemData?.message?.meta?.post_type === "post") {
          dispatch(setSavedPostList(updatedPostList));
          navigation.navigate("SavedPostList", {
            data: updatedPostList,
            screenName: "notification",
            notificationType: "comment",
            commentPostId: selectedItemData?.commentData?.post_id,
          });
        } else if (selectedItemData?.message?.meta?.post_type === "reel") {
          dispatch(setSavedReelList(updatedPostList));
          navigation.navigate("ViewReelForSave", {
            notificationType: "comment",
            commentPostId: selectedItemData?.commentData?.reel_id,
          });
        }
      }
    } else if (selectedItemData?.message?.meta?.notificationType === "follow") {
      if (
        selectedItemData?.followData &&
        !isEmpty(selectedItemData?.followData)
      ) {
        navigation.navigate("ProfileNew", {
          data: selectedItemData?.followData[0],
          type: "anotherProfile",
        });
      }
    } else if (selectedItemData?.message?.meta?.notificationType === "reel") {
      if (selectedItemData?.reelData && !isEmpty(selectedItemData?.reelData)) {
        const updatedReelList = {
          page: 1,
          next_enable: false,
          data: [selectedItemData?.reelData],
        };
        dispatch(setSavedReelList(updatedReelList));
        navigation.navigate("ViewReelForSave");
      }
    } else if (selectedItemData?.message?.meta?.notificationType === "chat") {
      if (
        selectedItemData?.message?.meta?.messageData?.userData &&
        selectedItemData?.message?.meta?.messageData?.conversation_id
      ) {
        navigation.navigate("MessagesInfo", {
          userInfo: {
            is_blocked: false,
            conversation_id:
              selectedItemData?.message?.meta?.messageData?.conversation_id ||
              "",
            userData: {
              user_dp:
                selectedItemData?.message?.meta?.messageData?.userData
                  ?.user_dp || "",
              user_id:
                selectedItemData?.message?.meta?.messageData?.userData
                  ?.user_id || "",
              full_name:
                selectedItemData?.message?.meta?.messageData?.userData
                  ?.full_name || "",
            },
          },
        });
      }
    } else if (
      selectedItemData?.message?.meta?.notificationType === "group_request"
    ) {
      navigation.navigate("GroupInfo", {
        groupId: selectedItemData?.message?.meta?.group_details_id,
        from: "notificaiton",
      });
    }
  };

  // Render swipeAble delete button
  const renderRightActions = (NotificationId) => {
    return (
      <View
        style={{
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <TouchableOpacity
          activeOpacity={0.8}
          style={styles.sendBtnMain}
          onPress={() => handleSingleDelete(NotificationId)}
        >
          <CustomIcon name={"Delete"} size={20} color={BaseColors.white} />
        </TouchableOpacity>
      </View>
    );
  };

  const RenderDataItem = ({ item, navigation }) => {
    useFocusEffect(
      useCallback(() => {
        swipeableRef.current.close();
      }, [])
    );

    return (
      <Swipeable
        renderRightActions={() => renderRightActions(item?.notification_id)}
        ref={swipeableRef}
      >
        <TouchableOpacity
          style={styles.renderItemMainView}
          activeOpacity={0.8}
          onPress={() => handleNavigation(item)}
        >
          {/* Image */}
          <View>
            {item?.image ? (
              <FastImage source={{ uri: item?.image }} style={styles.imgView} />
            ) : null}

            {/* green tick */}
            {item?.read ? null : <View style={styles.greenDot}></View>}
          </View>

          {/* User Titles */}
          <View style={styles.titleView}>
            {item?.message?.title ? (
              <Text style={styles.userNameViewText}>
                {truncate(startCase(item?.message?.title), {
                  length: 23,
                  omission: "...",
                })}
              </Text>
            ) : null}

            {/* Data and time */}

            {item?.time ? (
              <Text
                style={[
                  styles.timeText,
                  { color: item?.read ? BaseColors.gray11 : BaseColors.black },
                ]}
              >
                {item?.time}
              </Text>
            ) : null}

            {/* Notification message */}
            {item?.message?.body ? (
              <Text style={styles.messageText}>
                {truncate(startCase(item?.message?.body), {
                  length: 85,
                  omission: "...",
                })}
              </Text>
            ) : null}
          </View>
        </TouchableOpacity>
      </Swipeable>
    );
  };

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    fetchNotificationData();
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  }, []);

  const RenderDays = ({ item, navigation }) => {
    return (
      <View style={{ flex: 1 }}>
        {!isEmpty(item?.notifications?.data) ? (
          <View style={{ marginHorizontal: 20, marginVertical: 5 }}>
            <Text style={styles.userNameViewText}>
              {item?.notifications?.day}
            </Text>
          </View>
        ) : null}
        <FlatList
          data={item?.notifications?.data || []}
          renderItem={(item) => {
            return <RenderDataItem navigation={navigation} item={item?.item} />;
          }}
          showsVerticalScrollIndicator={false}
        />
      </View>
    );
  };

  const handleScroll = (event) => {
    const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;
    const isCloseToBottom =
      layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;
    if (isCloseToBottom && notificationData?.next_enable) {
      fetchNotificationData(notificationData?.page + 1, true);
    }
  };

  const handleGoBack = () => {
    navigation.goBack();
  };

  return (
    <SafeAreaView style={styles.main}>
      <CHeader
        handleBackButton={handleGoBack}
        headingTitle="notifications"
        clearAllNotification={() => setModalVisible(true)}
        clearBtn={!isEmpty(notificationData?.data) ? true : false}
      />

      <ScrollView
        onScroll={handleScroll}
        scrollEventThrottle={0.5}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[BaseColors.activeTab]} // Customize refresh indicator color
            tintColor={BaseColors.activeTab} // Customize refresh indicator color (Android)
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {loading ? (
          <MiniLoader size="medium" />
        ) : (
          <View style={{ flex: 1 }}>
            <FlatList
              data={notificationData?.data || []}
              renderItem={(item) => {
                return <RenderDays navigation={navigation} item={item?.item} />;
              }}
              showsVerticalScrollIndicator={false}
              ListEmptyComponent={
                <View style={styles.centerMain}>
                  <NoRecord
                    title={"Empty"}
                    type="chat"
                    description="noNotification"
                    iconName="Notification"
                  />
                </View>
              }
              style={{ marginBottom: Platform.OS === "ios" ? 50 : 0 }}
            />
            {bottomLoading ? (
              <View>
                <MiniLoader size="small" />
              </View>
            ) : null}
          </View>
        )}
      </ScrollView>
      <ModalComponent
        visible={visible}
        setModalVisible={setModalVisible}
        modalTitle={"Are you sure want to delete \n this Notification?"}
        buttonTxt={"Yes"}
        cancelText={"No"}
        isLoader={false}
        loading={modelButtonLoading}
        onClickSaveBtn={() => {
          if (isNumber(selectedNotification)) {
            deleteSingleNotification();
          } else {
            handleClearAllNotification();
          }
        }}
        centerIconName={"Delete"}
        modalHeder="Delete"
      />
    </SafeAreaView>
  );
};

export default NotificationScreen;
