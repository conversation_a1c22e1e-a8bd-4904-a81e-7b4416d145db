import { getApiData } from "@app/utils/apiHelper";
import BaseSetting from "@config/setting";
import { isEmpty } from "lodash-es";
import Toast from "react-native-simple-toast";

// Get Audience Data Form DB
export const groupInfo = async (group_id) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.groupInfo}?group_details_id=${group_id}`,
      "GET"
    );
    if (resp !== undefined) {
      if (resp?.data?.success) {
        return resp?.data;
      } else {
        Toast.show(
          resp?.data?.message || "Something went wrong please try again"
        );
      }
    }
  } catch (error) {
    console.error("🚀 ~ natureOfBusinessData ~ error:", error);
  }
};

export const groupmemberList = async (group_id, page) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.groupmemberList}?group_details_id=${group_id}&page=${page}`,
      "GET"
    );
    if (resp !== undefined) {
      if (resp?.data?.success) {
        return resp?.data;
      } else {
        Toast.show(
          resp?.data?.message || "Something went wrong please try again"
        );
      }
    }
  } catch (error) {
    console.error("🚀 ~ natureOfBusinessData ~ error:", error);
  }
};

export const addGroupMember = async (group_id, new_member_id) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.addGroupMember}`,
      "POST",
      {
        group_details_id: group_id,
        new_member_id: new_member_id,
      }
    );
    if (resp?.data.success) {
      Toast.show(resp?.data?.message);
      return resp?.data;
    } else {
      Toast.show(
        resp?.data?.message || "Something went wrong please try again"
      );
    }
  } catch (error) {
    console.error("🚀 ~ natureOfBusinessData ~ error:", error);
  }
};

export const removeMember = async (group_id, new_member_id) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.removerGroupmember}`,
      "POST",
      {
        group_details_id: group_id,
        member_id_to_remove: new_member_id,
      }
    );
    if (resp !== undefined) {
      if (resp?.data?.success) {
        Toast.show(resp?.data?.message);
        return resp?.data;
      } else {
        Toast.show(
          resp?.data?.message || "Something went wrong please try again"
        );
      }
    }
  } catch (error) {
    console.error("🚀 ~ natureOfBusinessData ~ error:", error);
  }
};

export const markAsAdmin = async (group_id, new_member_id) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.makeGroupAdmin}`,
      "POST",
      {
        group_details_id: group_id,
        member_id_to_promote: new_member_id,
      }
    );
    if (resp !== undefined) {
      if (resp?.data?.success) {
        Toast.show(resp?.data?.message);
        return resp?.data;
      } else {
        Toast.show(
          resp?.data?.message || "Something went wrong please try again"
        );
      }
    }
  } catch (error) {
    console.error("🚀 ~ natureOfBusinessData ~ error:", error);
  }
};

export const groupRequestList = async (group_id, type, page) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.groupRequestList}?group_details_id=${group_id}&status=${type}&page=${page}`,
      "GET"
    );
    console.log("🚀 ~ groupRequestList ~ resp:", resp);
    if (resp !== undefined) {
      if (resp?.data?.success) {
        return resp?.data;
      } else {
        Toast.show(
          resp?.data?.message || "Something went wrong please try again"
        );
      }
    }
  } catch (error) {
    console.error("🚀 ~ natureOfBusinessData ~ error:", error);
  }
};

// Get Audience Data Form DB
export const updateGropImage = async (data) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.groupUpdateIcon}`,
      "POST",
      data,
      {},
      true
    );
    if (resp !== undefined) {
      if (resp?.data?.success) {
        Toast.show(resp?.data);
        return resp?.data;
      } else {
        Toast.show(
          resp?.data?.message || "Something went wrong please try again"
        );
      }
    }
  } catch (error) {
    console.error("🚀 ~ natureOfBusinessData ~ error:", error);
  }
};
