import React, { useCallback, useEffect, useState } from "react";
import {
  View,
  Text,
  KeyboardAvoidingView,
  FlatList,
  ScrollView,
  Modal,
  ActivityIndicator,
  Dimensions,
  TouchableOpacity,
} from "react-native";
import styles from "./styles";
import { BaseColors } from "@config/theme";
import CHeader from "@components/CHeader";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import { SafeAreaView } from "react-native";
import { FontFamily } from "@config/typography";
import { CustomIcon } from "@config/LoadIcons";
import { Image } from "react-native";
import Iicon from "react-native-vector-icons/Ionicons";
import CButton from "@components/CButton";
import {
  addGroupMember,
  groupInfo,
  groupmemberList,
  groupRequestList,
  markAsAdmin,
  removeMember,
  updateGropImage,
} from "./apiCallFunction";
import { useFocusEffect } from "@react-navigation/native";
import { useSelector } from "react-redux";
import CSearch from "@components/CSearch";
import { isEmpty, isNull } from "lodash-es";
import {
  getChatSearchData,
  getGroupMemberList,
  onRequestAction,
} from "@screens/Chat/apiCallFunction";
import { images } from "@config/images";
import FastImage from "react-native-fast-image";
import GroupConformationModal from "@components/GroupConformationModal";
import NoRecord from "@components/NoRecord";
import { pickDocument } from "@screens/SingUpWithCompanyDetail/apiFunctions";
import CCamera from "@components/CameraButton/CCamera";
import AlreadyHaveStoryModal from "@components/AlreadyHaveStoryModal";
import MoreInfoModal from "@components/MoreInfoModal";
import { optionMenuData } from "@config/staticData";
// import { translate } from "@language/Translate";

/**
 * ChatTab Component
 *
 * Main chat interface component that provides a tabbed view for different chat types.
 * Features include:
 * - Tab navigation between Personal chats and Group chats
 * - Search functionality for finding chats
 * - Block list management
 * - User subscription validation for premium features
 * - Real-time chat updates via socket connection
 *
 * @param {Object} navigation - React Navigation object for screen navigation
 */
const GroupInfo = ({ navigation, route }) => {
  const GroupText = [
    { id: 1, key: "members", text: "Members" },
    { id: 2, key: "pending", text: "Request" },
    { id: 3, key: "reject", text: "Rejected" },
  ];

  // ==================== STATE MANAGEMENT ====================

  // Modal and UI states
  const from = route?.params?.from || "";
  const pressFrom = route?.params?.pressFrom || "";
  const [selectId, setSelectId] = useState(from === "notificaiton" ? 2 : 1);
  const groupId = route?.params?.groupId || "";
  const [groupinfo, setGroupInfo] = useState({}); // Shows/hides block list screen
  const [actionTab, setActionTab] = useState(GroupText);
  const [visible, setModalVisible] = useState(false); // Modal visibility state
  const [searchBtnData, setSearchBtnData] = useState(null); // Search button data
  const [searchChatList, setSearchChatList] = useState([]);
  const [userList, setUserList] = useState([]);
  const [addedMembers, setAddedMembers] = useState([]);
  const [memberList, setMemberList] = useState([]);
  // Authentication and subscription states
  const { isCurrentPlan, activePlanData, userData } = useSelector(
    (a) => a.auth
  );
  const [typingTimeout, setTypingTimeout] = useState(null); // Debounce timer for search input
  const [loading, setLoading] = useState(false);
  const [addLoader, setAddLoader] = useState(false);
  const [removeModalVisible, setRemoveModalVisible] = useState(false);
  const [requestAction, setRequestAction] = useState({
    visible: false,
    text: "",
    header: "",
    data: {},
    from: "",
  });
  const [removeItemData, setRemoveitemData] = useState({ data: {}, type: "" });
  const [selectUserId, setSelectUserId] = useState("");
  const [isVisible, setisVisible] = useState(false);
  const [selectOption, setSelectOption] = useState({});
  const [memberListLoader, setMemberListLoader] = useState(false);
  const [profilePictureModal, setProfilePictureModal] = useState(false);
  const [isCameraOpen, setIsCameraOpen] = useState(false);
  const [imagePath, setImagePath] = useState({});
  const [imageLoader, setImageLoader] = useState(false);
  const [actionVisible, setActionVisible] = useState(false);
  const [selectionAction, setSelectionAction] = useState([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const [isPaginationLoading, setIsPaginationLoading] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(false);

  // ==================== DAYJS CONFIGURATION ====================
  dayjs.extend(utc); // Extend dayjs with UTC plugin for timezone handling

  const fetchGroupInfo = async (
    group_id,
    type = "",
    pageNumber = 1,
    paginationType = ""
  ) => {
    // Prevent multiple concurrent API calls for pagination
    if (paginationType === "pagination" && isPaginationLoading) {
      return;
    }

    if (paginationType === "pagination") {
      setIsPaginationLoading(true);
    } else {
      setIsInitialLoading(true);
      setMemberListLoader(true);
    }

    const resp = await groupInfo(group_id);
    setGroupInfo(resp?.data);

    if (type !== "notificaiton") {
      const listResp = await groupmemberList(group_id, pageNumber);
      if (listResp?.success) {
        setImageLoader(false);
        if (pageNumber === 1) {
          setMemberList(listResp.data);
        } else {
          setMemberList([...memberList, ...listResp.data]);
        }
        setPage(Number(listResp?.pagination?.currentPage));
        if (listResp?.pagination?.hasNextPage) {
          setHasMore(true);
        } else {
          setHasMore(false);
        }
        setMemberListLoader(false);
        setIsInitialLoading(false);
        setIsPaginationLoading(false);
      } else {
        setImageLoader(false);
        setMemberList([]);
        setMemberListLoader(false);
        setIsInitialLoading(false);
        setIsPaginationLoading(false);
      }
    } else {
      setMemberListLoader(false);
      setIsInitialLoading(false);
      setIsPaginationLoading(false);
    }
  };
  const getUserShareList = useCallback(
    async (pageNumber = 1, paginationType = "") => {
      // Prevent multiple concurrent API calls for pagination
      if (paginationType === "pagination" && isPaginationLoading) {
        return;
      }

      if (paginationType === "pagination") {
        setIsPaginationLoading(true);
      } else {
        setIsInitialLoading(true);
      }

      const resp = await getGroupMemberList(
        userData?.user_id,
        "",
        groupId,
        pageNumber
      );
      if (resp?.success) {
        if (pageNumber === 1) {
          setUserList(resp.data);
        } else {
          setUserList([...userList, ...resp.data]);
        }
        setPage(Number(resp?.pagination?.currentPage));
        // check if there is more data
        if (resp?.pagination.nextPage) {
          setHasMore(true);
        } else {
          setHasMore(false);
        }
        setIsInitialLoading(false);
        setIsPaginationLoading(false);
      } else {
        setIsInitialLoading(false);
        setIsPaginationLoading(false);
      }
    },
    [userData, userList, isPaginationLoading, groupId]
  );

  useFocusEffect(
    useCallback(() => {
      fetchGroupInfo(groupId, from);
      getUserShareList();
    }, [groupId, from])
  );

  useEffect(() => {
    let fiterTab;
    if (groupinfo?.isReqRejTab) {
      fiterTab = GroupText;
    } else {
      fiterTab = GroupText.filter((item) => item.id === 1);
    }
    setActionTab(fiterTab);
    setAddedMembers(memberList);
  }, [groupinfo, memberList]);

  const renderMemberList = ({ item }) => {
    const is_Admin = memberList?.find(
      (li) => li.user_id === userData?.user_id
    )?.is_admin;
    return (
      <TouchableOpacity
        style={styles.userRow}
        activeOpacity={0.8}
        onPress={() => {
          if (
            is_Admin === 1 &&
            userData?.user_id !== item.user_id &&
            item?.is_superadmin === 0
          ) {
            setisVisible(true);
            setSelectOption(item);
          }
        }}
      >
        {item.profile_picture ? (
          <FastImage
            source={{ uri: item.profile_picture }}
            style={styles.avatar}
          />
        ) : (
          <FastImage
            source={
              userData?.gender === "male" ? images.manAvatar : images.manAvatar
            }
            style={styles.avatar}
          />
        )}
        <View style={styles.info}>
          <Text style={styles.name}>{item.full_name}</Text>
          {item.is_admin === 1 ? (
            <Text style={styles.role}>{"Admin"}</Text>
          ) : null}
        </View>
        {is_Admin === 1 &&
          userData?.user_id !== item.user_id &&
          item?.is_superadmin === 0 &&
          selectId === 1 && (
            <TouchableOpacity
              activeOpacity={0.7}
              onPress={() => {
                setisVisible(true);
                setSelectOption(item);
              }}
            >
              <CustomIcon name="BsChevronRight" color={BaseColors.black} />
            </TouchableOpacity>
          )}
        <View style={styles.actions}>
          {selectId === 2 && (
            <>
              <TouchableOpacity
                style={styles.removeButton}
                onPress={() => {
                  setRequestAction({
                    visible: true,
                    text: `Are you sure you want to add ${item.full_name} in to Group? `,
                    header: "Accept Request",
                    data: item,
                    from: "group",
                  });
                }}
                activeOpacity={0.8}
              >
                <Iicon name="checkbox-outline" size={20} color={"#1C274C"} />
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.removeButton}
                onPress={() => {
                  setRequestAction({
                    visible: true,
                    text: `Are you sure you want to Reject ${item.full_name} from to Group? `,
                    header: "Reject Request",
                    data: item,
                    from: "group",
                  });
                }}
              >
                <Iicon
                  name="close-circle-outline"
                  size={22}
                  color={"#F75555"}
                />
              </TouchableOpacity>
            </>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const handleSearchMember = async () => {
    const resp = await getChatSearchData(
      searchBtnData?.trim(),
      searchChatList?.page,
      groupId
    );
    if (resp?.data?.success && !isEmpty(resp?.data?.data)) {
      setSearchChatList({
        page: resp?.data?.currentPage,
        next_enable: resp?.data?.hasNextPage,
        data:
          resp?.data?.currentPage > 1
            ? [...searchChatList?.data, ...resp?.data?.data] // Append for pagination
            : resp?.data?.data, // Replace for new search
      });
    }
  };

  const HandleSelectImage = async (type) => {
    const data = await pickDocument(type, imagePath);
    setProfilePictureModal(false);
    setImageLoader(true);
    const imageData = {
      image: data,
      group_details_id: groupinfo?.group_details_id,
    };
    const resp = await updateGropImage(imageData);
    if (resp?.success) {
      fetchGroupInfo(groupId);
    } else {
      setImageLoader(false);
    }
  };

  useEffect(() => {
    if (isCameraOpen) {
      setProfilePictureModal(false);
    }
    if (!isCameraOpen && imagePath?.uri) {
      setProfilePictureModal(false);
      HandleSelectImage("captureImg");
    }
  }, [isCameraOpen]);

  useEffect(() => {
    if (pressFrom === "memberIcon") {
      setModalVisible(true);
      getUserShareList();
    }
  }, [pressFrom]);

  /**
   * Removes a member from the group after confirmation
   *
   * @param {Object} item - User object to remove
   */
  const handleRemoveMember = async (item) => {
    try {
      setLoading(true);
      const resp = await removeMember(groupId, item.user_id);

      if (resp?.success) {
        // Close modal first
        setRemoveModalVisible(false);
        setActionVisible(false);
        if (removeItemData?.type === "modal") {
          setTimeout(() => {
            setModalVisible(true);
          }, 300);
        }
        setRemoveitemData({ data: {}, type: "" });

        // Refresh group info to get updated member list
        await fetchGroupInfo(groupId);
        setLoading(false);
      }
    } catch (error) {
      console.error("🚀 ~ handleRemoveMember ~ error:", error);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Removes a member from the group after confirmation
   *
   * @param {Object} item - User object to remove
   */
  const handleMakeAdmin = async (item, type = "") => {
    try {
      setAddLoader(true);
      const resp = await markAsAdmin(groupId, item.user_id);

      if (resp?.success) {
        // Close modal first
        setisVisible(false);
        setActionVisible(false);
        if (type === "modal") {
          setTimeout(() => {
            setModalVisible(true);
          }, 300);
        }
        setSelectOption({});
        // Refresh group info to get updated member list
        await fetchGroupInfo(groupId);
        setAddLoader(false);
      } else {
        setAddLoader(false);
      }
    } catch (error) {
      setAddLoader(false);
      console.error("🚀 ~ handleRemoveMember ~ error:", error);
    } finally {
      setAddLoader(false);
    }
  };

  const handleOpenOption = (item) => {
    if (item?.status === "Remove") {
      if (item?.is_admin === 1) {
        setSelectionAction([
          {
            id: 1,
            title: "Remove User",
          },
        ]);
      } else {
        setSelectionAction([
          {
            id: 1,
            title: "Remove User",
          },
          {
            id: 2,
            title: "Make as Admin",
          },
        ]);
      }
    } else {
      setSelectionAction([
        {
          id: 1,
          title: "Add User",
        },
      ]);
    }
    setModalVisible(false);
    setTimeout(() => {
      setActionVisible(true);
      setSelectOption(item);
    }, 300);
  };

  const handleAddGroupMember = async (item) => {
    setAddLoader(true);
    const resp = await addGroupMember(groupId, item?.user_id);
    if (resp?.success) {
      setAddLoader(false);
      await handleSearchMember("", 1);
      await getUserShareList();
      await fetchGroupInfo(groupId);
    } else {
      setAddLoader(false);
    }
  };

  const handleAction = async (e) => {
    if (e?.title === "Remove User") {
      setActionVisible(false);
      setTimeout(() => {
        setRemoveModalVisible(true);
        setRemoveitemData({ data: selectOption, type: "modal" });
      }, 300);
    } else if (e?.title === "Make as Admin") {
      handleMakeAdmin(selectOption, "modal");
    }
  };

  const renderUserList = ({ item, index }) => {
    return (
      <TouchableOpacity
        key={item.id}
        style={{
          flexDirection: "row",
          alignItems: "center",
          marginBottom: 10,
        }}
        activeOpacity={0.8}
        onPress={() => {
          if (item.status === "Remove") {
            handleOpenOption(item);
          }
        }}
        disabled={groupinfo?.is_admin ? false : true}
      >
        {/* User Profile Image */}
        {item.user_dp ? (
          <FastImage
            source={{ uri: item.user_dp }}
            style={styles.memberImageSize}
          />
        ) : (
          <FastImage
            source={
              userData?.gender === "male" ? images.manAvatar : images.manAvatar
            }
            style={styles.memberImageSize}
          />
        )}

        {/* User Info */}
        <View style={{ marginLeft: 10 }}>
          <Text
            style={{
              fontSize: 18,
              fontFamily: FontFamily.RobotoMedium,
              marginBottom: 5,
              width: Dimensions.get("window").width / 1.9,
            }}
          >
            {item.full_name}
          </Text>
        </View>

        {/* Add/Remove Button */}
        {!groupinfo?.is_admin ? null : (
          <View
            style={{
              flex: 1,
              alignItems: "flex-end",
            }}
          >
            <View>
              {item.status === "Remove" ? (
                <TouchableOpacity
                  style={{ marginRight: 10 }}
                  onPress={() => {
                    handleOpenOption(item);
                  }}
                >
                  <CustomIcon name="BsChevronRight" color={BaseColors.black} />
                </TouchableOpacity>
              ) : item.status === "Requested" ? (
                <View style={{ flexDirection: "row" }}>
                  <TouchableOpacity
                    style={styles.removeButton}
                    onPress={() => {
                      setModalVisible(false);
                      setTimeout(() => {
                        setRequestAction({
                          visible: true,
                          text: `Are you sure you want to add ${item.full_name} in to Group? `,
                          header: "Accept Request",
                          data: item,
                          from: "modal",
                        });
                      }, 300);
                    }}
                    activeOpacity={0.8}
                  >
                    <Iicon
                      name="checkbox-outline"
                      size={24}
                      color={"#1C274C"}
                    />
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.removeButton}
                    onPress={() => {
                      setModalVisible(false);
                      setTimeout(() => {
                        setRequestAction({
                          visible: true,
                          text: `Are you sure you want to Reject ${item.full_name} from to Group? `,
                          header: "Reject Request",
                          data: item,
                          from: "modal",
                        });
                      }, 300);
                    }}
                  >
                    <Iicon
                      name="close-circle-outline"
                      size={26}
                      color={"#F75555"}
                    />
                  </TouchableOpacity>
                </View>
              ) : (
                <TouchableOpacity
                  style={{
                    paddingHorizontal: 10,
                    paddingVertical: 6,
                    borderWidth: 1,
                    borderRadius: 5,
                    borderColor: BaseColors.activeTab,
                    backgroundColor:
                      item.status === "Remove"
                        ? BaseColors.white
                        : BaseColors.activeTab,
                  }}
                  activeOpacity={0.8}
                  onPress={() => {
                    setSelectUserId(item.user_id);
                    handleAddGroupMember(item);
                  }}
                >
                  {addLoader && selectUserId === item.user_id ? (
                    <ActivityIndicator
                      animating
                      color={BaseColors.White}
                      size="small"
                    />
                  ) : (
                    <Text
                      style={{
                        color:
                          item.status === "Remove"
                            ? BaseColors.activeTab
                            : BaseColors.white,
                      }}
                    >
                      {item.status}
                    </Text>
                  )}
                </TouchableOpacity>
              )}
            </View>
          </View>
        )}
      </TouchableOpacity>
    );
  };

  /**
   * Renders individual member card in horizontal FlatList
   * Shows user image, name, and remove button
   *
   * @param {Object} item - User object
   * @param {number} index - Array index
   */
  const renderItem = ({ item, index }) => {
    return (
      <View style={{ marginHorizontal: 5 }}>
        {item.profile_picture !== null ? (
          <FastImage
            source={{ uri: item.profile_picture }}
            style={styles.memberImageSize}
          />
        ) : (
          <FastImage
            source={
              userData?.gender === "male" ? images.manAvatar : images.manAvatar
            }
            style={styles.memberImageSize}
          />
        )}

        {/* User Name */}
        <Text
          style={{
            fontSize: 14,
            fontFamily: FontFamily.RobotoRegular,
            width: 50,
          }}
          numberOfLines={1}
        >
          {item.full_name}
        </Text>
      </View>
    );
  };

  /**
   * Handles user search API call
   *
   * @param {string} searchBtnData - Search query (trimmed)
   * @param {number} page - Page number for pagination (default: 1)
   */
  const handleSearchChatList = async (
    searchBtnData,
    pageNumber = 1,
    paginationType
  ) => {
    if (groupinfo?.group_type === "private") {
      if (paginationType === "pagination" && isPaginationLoading) {
        return;
      }

      if (paginationType === "pagination") {
        setIsPaginationLoading(true);
      } else {
        setIsInitialLoading(true);
      }

      const resp = await getGroupMemberList(
        userData?.user_id,
        searchBtnData?.trim(),
        groupId,
        pageNumber
      );
      if (resp?.success) {
        if (pageNumber === 1) {
          setUserList(resp.data);
        } else {
          setUserList([...userList, ...resp.data]);
        }
        setPage(Number(resp?.pagination?.currentPage));
        // check if there is more data
        if (resp?.pagination.nextPage) {
          setHasMore(true);
        } else {
          setHasMore(false);
        }
        setIsInitialLoading(false);
        setIsPaginationLoading(false);
      } else {
        setIsInitialLoading(false);
        setIsPaginationLoading(false);
      }
    } else {
      if (paginationType === "pagination" && isPaginationLoading) {
        return;
      }

      if (paginationType === "pagination") {
        setIsPaginationLoading(true);
      } else {
        setIsInitialLoading(true);
      }
      const resp = await getChatSearchData(
        searchBtnData?.trim(),
        pageNumber,
        groupId
      );
      console.log("🚀 ~ handleSearchChatList ~ resp:", resp?.data);
      if (resp?.data?.success && !isEmpty(resp?.data?.data)) {
        setSearchChatList({
          page: pageNumber,
          next_enable: resp?.data?.hasNextPage,
          data:
            pageNumber > 1
              ? [...searchChatList?.data, ...resp?.data?.data] // Append for pagination
              : resp?.data?.data, // Replace for new search
        });
        setPage(Number(resp?.data?.currentPage));
        // check if there is more data
        if (resp?.data.hasNextPage) {
          setHasMore(true);
        } else {
          setHasMore(false);
        }
        setIsInitialLoading(false);
        setIsPaginationLoading(false);
      } else {
        setIsInitialLoading(false);
        setIsPaginationLoading(false);
      }
    }
  };

  // ==================== SEARCH FUNCTIONALITY ====================

  /**
   * Handles search input with debouncing
   * Delays API call by 1 second after user stops typing
   * Only executes if user has valid subscription plan
   *
   * @param {string} searchBtnData - The search query entered by user
   */
  const handleInputChange = useCallback(
    (searchBtnData) => {
      clearTimeout(typingTimeout);
      setTypingTimeout(
        setTimeout(async () => {
          // Check if user has valid subscription before allowing search
          if (
            !isEmpty(isCurrentPlan) ||
            activePlanData?.is_prime_user ||
            activePlanData?.is_free_user
          ) {
            handleSearchChatList(searchBtnData, 1);
          }
        }, 1000)
      );
    },
    [typingTimeout, searchBtnData]
  );

  // ==================== SIDE EFFECTS ====================

  /**
   * Trigger search when searchBtnData changes
   * Only if user has valid subscription
   */
  useEffect(() => {
    if (
      !isEmpty(isCurrentPlan) ||
      activePlanData?.is_prime_user ||
      activePlanData?.is_free_user
    ) {
      if (!isNull(searchBtnData)) {
        handleInputChange(searchBtnData);
      }
    }
  }, [searchBtnData]);

  useEffect(() => {
    if (visible) {
      handleSearchMember();
      getUserShareList();
      fetchGroupInfo(groupId);
    }
  }, [visible]);

  const changeTabeVal = async (
    selectType,
    pageNumber = 1,
    paginationType = ""
  ) => {
    if (selectType === "members") {
      fetchGroupInfo(groupId, "", pageNumber, paginationType);
    } else {
      // Prevent multiple concurrent API calls for pagination
      if (paginationType === "pagination" && isPaginationLoading) {
        return;
      }

      if (paginationType === "pagination") {
        setIsPaginationLoading(true);
      } else {
        setIsInitialLoading(true);
        setMemberListLoader(true);
      }

      const resp = await groupRequestList(groupId, selectType, pageNumber);
      console.log("🚀 ~ changeTabeVal ~ resp:", resp);
      if (resp?.success) {
        setMemberListLoader(false);
        if (pageNumber === 1) {
          setMemberList(resp.data);
        } else {
          setMemberList([...memberList, ...resp.data]);
        }
        setPage(Number(resp?.pagination?.currentPage));
        if (resp?.pagination.hasNextPage) {
          setHasMore(true);
        } else {
          setHasMore(false);
        }
        setIsInitialLoading(false);
        setIsPaginationLoading(false);
      } else {
        setMemberListLoader(false);
        setMemberList([]);
        setIsInitialLoading(false);
        setIsPaginationLoading(false);
      }
    }
  };

  useEffect(() => {
    if (from === "notificaiton") {
      changeTabeVal("pending");
    }
  }, [from]);

  const handleRequestAction = async () => {
    const action = selectId === 2 ? "pending" : "reject";
    const actiontext =
      requestAction?.header === "Accept Request" ? "accept" : "reject";
    const resp = await onRequestAction(
      requestAction?.data?.group_request_id,
      actiontext
    );
    if (resp?.success) {
      setRequestAction({
        visible: false,
        text: "",
        header: "",
        data: {},
        from: "",
      });
      if (requestAction?.from === "modal") {
        setTimeout(() => {
          setModalVisible(true);
        }, 300);
      }
      changeTabeVal(action);
      const resp = await groupInfo(groupId);
      setGroupInfo(resp?.data);
    }
  };

  const noRecordView = () => {
    return <NoRecord title="noRecordFound" />;
  };

  const handleToOptionMenu = (e) => {
    if (e.id === 1) {
      handleMakeAdmin(selectOption, "");
    } else if (e.id === 2) {
      setisVisible(false);
      setTimeout(() => {
        setRemoveModalVisible(true);
        setRemoveitemData({ data: selectOption, type: "" });
      }, 300);
    }
  };

  const handleScroll = (event) => {
    const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;
    const isCloseToBottom =
      layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;
    let bottom = true;
    if (isCloseToBottom && hasMore && !isInitialLoading) {
      const nextPage = page + 1;
      if (searchBtnData) {
        handleSearchChatList(searchBtnData, nextPage, "pagination");
      } else {
        getUserShareList(nextPage, "pagination");
      }
    }
  };

  const onEndReached = () => {
    if (hasMore && !isInitialLoading) {
      const selectType =
        selectId === 1 ? "members" : selectId === 2 ? "pending" : "reject";
      const nextPage = page + 1;
      changeTabeVal(selectType, nextPage, "pagination");
    }
  };

  // ==================== MAIN RENDER ====================
  return (
    <SafeAreaView style={styles.main} nestedScrollEnabled>
      {isCameraOpen ? (
        <View style={{ flex: 1 }}>
          <CCamera
            setImagePath={setImagePath}
            isCameraOpen={isCameraOpen}
            setIsCameraOpen={setIsCameraOpen}
            recordVideo={false}
          />
        </View>
      ) : (
        <>
          {/* ==================== HEADER SECTION ==================== */}
          <CHeader
            handleBackButton={() => navigation.goBack()}
            headingTitle="Group Info"
          />
          <ScrollView style={{ flex: 1 }} behavior="padding">
            {/* Group Profile Picture Section */}
            <View style={styles.profileImgMainViewStyle}>
              <TouchableOpacity
                style={styles.profileImgMainViewStyle}
                activeOpacity={0.8}
                onPress={() => setProfilePictureModal(true)}
                disabled={groupinfo?.isReqRejTab ? false : true}
              >
                <View style={styles.profileImgMainView}>
                  {imageLoader ? (
                    <ActivityIndicator />
                  ) : (
                    <FastImage
                      source={{
                        uri: groupinfo?.image_url,
                      }}
                      style={styles.profileImgStyle}
                      resizeMode={FastImage.resizeMode.cover}
                    />
                  )}
                </View>
                {groupinfo?.isReqRejTab ? (
                  <TouchableOpacity
                    style={[styles.editMainViewStyle]}
                    activeOpacity={0.9}
                    onPress={() => setProfilePictureModal(true)}
                  >
                    <CustomIcon
                      name={"Edit-Square"}
                      size={25}
                      color={BaseColors.activeTab}
                    />
                  </TouchableOpacity>
                ) : null}
              </TouchableOpacity>
            </View>

            {/* Group Name and Member Count */}
            <View style={styles.groupHeaderContainer}>
              <Text style={styles.grouoTitleText}>{groupinfo?.group_name}</Text>
              <Text style={styles.groupmemberText}>
                Group - {groupinfo?.member_count} Members
              </Text>
            </View>

            {/* Group Information */}
            <View
              style={[
                styles.groupContentContainer,
                { marginBottom: groupinfo?.description ? 5 : 16 },
              ]}
            >
              <View style={styles.nobContainer}>
                <Text style={styles.nobText}>Nature of business</Text>
                <Text style={styles.nobType}>
                  {groupinfo?.nature_business
                    ?.map((item) => item.name)
                    .join(", ")}
                </Text>
              </View>
              <View style={styles.gropSubContainer}>
                <Text style={styles.groupText}>Group</Text>
                <Text style={styles.groupTypeText}>
                  {groupinfo?.group_type === "private"
                    ? "Private"
                    : groupinfo?.group_type === "public"
                      ? "Public"
                      : "Auto Join"}
                </Text>
              </View>
            </View>

            {groupinfo?.description ? (
              <View
                style={[styles.groupContentContainer, { marginBottom: 20 }]}
              >
                <Text
                  style={[
                    styles.nobType,
                    { paddingHorizontal: 10, fontSize: 14 },
                  ]}
                >
                  {groupinfo?.description}
                </Text>
              </View>
            ) : null}

            {/* Group Action */}
            <View style={styles.gropAction}>
              <View style={styles.gropActionSubContainer}>
                {actionTab.map((item) => (
                  <TouchableOpacity
                    activeOpacity={0.8}
                    key={item.id}
                    style={[
                      styles.groupActionContainer,
                      {
                        backgroundColor:
                          selectId === item.id
                            ? BaseColors.activeTab
                            : BaseColors.white,
                        borderWidth: selectId === item.id ? 1 : 0,
                      },
                    ]}
                    onPress={() => {
                      setSelectId(item.id);
                      changeTabeVal(item.key);
                    }}
                  >
                    <Text
                      style={[
                        styles.groupActionText,
                        {
                          color:
                            selectId === item.id
                              ? BaseColors.white
                              : BaseColors.lightBlack10,
                        },
                      ]}
                    >
                      {item.text}
                      {item.key === "pending"
                        ? `(${groupinfo?.group_request_count})`
                        : item.key === "reject"
                          ? `(${groupinfo?.group_rejected_count})`
                          : null}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
              {/* <View>
                <CustomIcon name="Search-1" size={20} />
              </View> */}
            </View>

            {/* Group Member List */}
            <View
              style={[
                styles.groupContentContainer,
                { paddingHorizontal: 10, alignItem: "center" },
              ]}
            >
              {isInitialLoading ? (
                <View
                  style={{
                    flex: 1,
                    justifyContent: "center",
                    alignItems: "center",
                    paddingVertical: 50,
                  }}
                >
                  <ActivityIndicator
                    size="large"
                    color={BaseColors.activeTab}
                  />
                </View>
              ) : (
                <FlatList
                  data={memberList || []}
                  nestedScrollEnabled
                  renderItem={renderMemberList}
                  showsVerticalScrollIndicator={false}
                  maxHeight={Dimensions.get("window").height / 2}
                  ListEmptyComponent={noRecordView}
                  onEndReached={onEndReached}
                  onEndReachedThreshold={0.8}
                  ListFooterComponent={() => {
                    if (isPaginationLoading && hasMore) {
                      return (
                        <View
                          style={{
                            paddingVertical: 20,
                            alignItems: "center",
                          }}
                        >
                          <ActivityIndicator
                            size="small"
                            color={BaseColors.activeTab}
                          />
                        </View>
                      );
                    }
                    return null;
                  }}
                  contentContainerStyle={{ flexGrow: 1 }}
                  keyExtractor={(item, index) => `${item.user_id}-${index}`}
                />
              )}
            </View>
          </ScrollView>
          <View style={styles.buttonConiner}>
            <CButton
              type="outlined"
              onBtnClick={() => {
                setModalVisible(true);
                getUserShareList();
              }}
            >
              See All
            </CButton>
          </View>

          <Modal
            animationType="slide"
            transparent={true}
            animationInTiming={5000}
            animationOutTiming={5000}
            visible={visible}
            onRequestClose={() => {
              setModalVisible(!visible);
            }}
          >
            <View style={styles.ovarlayStyle}>
              <View style={styles.modalView}>
                {/* Modal Header */}
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "space-between",
                  }}
                >
                  <Text style={styles.modalTitleText}>{"Add Members"}</Text>

                  {/* Close Button */}
                  <TouchableOpacity
                    style={{
                      borderWidth: 1,
                      height: 24,
                      width: 24,
                      borderRadius: 5,
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                    activeOpacity={0.8}
                    onPress={() => {
                      setModalVisible(false);
                      setSearchBtnData(null);
                    }}
                  >
                    <CustomIcon name="BsX" size={20} color={BaseColors.black} />
                  </TouchableOpacity>
                </View>

                {/* Search Component */}
                <View
                  style={[
                    styles.searchView,
                    { marginHorizontal: 0, marginBottom: 20 },
                  ]}
                >
                  <CSearch
                    optionData={[
                      {
                        key: "block",
                        icon: "material-symbols_block",
                        text: "Block List",
                      },
                    ]}
                    searchBtnData={searchBtnData}
                    setSearchBtnData={setSearchBtnData}
                  />
                </View>

                {/* Selected Members Horizontal List */}
                <View
                  style={{
                    borderWidth: 0.6,
                    borderRadius: 5,
                    borderColor: "#8E8383",
                    paddingHorizontal: 10,
                    paddingVertical: 15,
                    marginBottom: 20,
                  }}
                >
                  <FlatList
                    data={addedMembers}
                    renderItem={renderItem}
                    horizontal={true}
                    keyExtractor={(item, index) => item.user_id}
                    showsHorizontalScrollIndicator={false}
                  />
                </View>

                {/* Available Users Vertical List */}
                <ScrollView
                  onScroll={handleScroll}
                  scrollEventThrottle={16}
                  keyboardShouldPersistTaps="handled"
                  showsVerticalScrollIndicator={false}
                  nestedScrollEnabled
                >
                  <FlatList
                    data={
                      !isEmpty(searchBtnData)
                        ? !isEmpty(searchChatList?.data) &&
                          !isNull(searchBtnData)
                          ? searchChatList?.data
                          : userList
                        : userList
                    }
                    renderItem={renderUserList}
                    showsVerticalScrollIndicator={false}
                    keyboardShouldPersistTaps="handled"
                    ListEmptyComponent={
                      isInitialLoading ? (
                        <View
                          style={{
                            flex: 1,
                            justifyContent: "center",
                            alignItems: "center",
                            paddingVertical: 50,
                          }}
                        >
                          <ActivityIndicator
                            size="large"
                            color={BaseColors.activeTab}
                          />
                        </View>
                      ) : (
                        <View style={styles.centerMain}>
                          <NoRecord title="noRecordFound" />
                        </View>
                      )
                    }
                    ListFooterComponent={() => {
                      if (isPaginationLoading && hasMore) {
                        return (
                          <View
                            style={{
                              paddingVertical: 20,
                              alignItems: "center",
                            }}
                          >
                            <ActivityIndicator
                              size="small"
                              color={BaseColors.activeTab}
                            />
                          </View>
                        );
                      }
                      return null;
                    }}
                    style={{ flex: 1 }}
                    contentContainerStyle={{ paddingBottom: 20, flexGrow: 1 }}
                    keyExtractor={(item, index) => `${item.user_id}-${index}`}
                  />
                </ScrollView>
              </View>
            </View>
          </Modal>

          {/* Remove Member Confirmation Modal */}
          <GroupConformationModal
            visible={removeModalVisible}
            header={"Remove Member"}
            content={`Are you sure you want to remove ${removeItemData?.data?.full_name} from the group?`}
            optionOneLabel={"Yes"}
            optionTwoLabel={"Cancel"}
            onOptionOne={() => {
              handleRemoveMember(removeItemData?.data);
            }}
            loading={loading}
            onOptionTwo={() => {
              if (removeItemData?.type === "modal") {
                setTimeout(() => {
                  setModalVisible(true);
                }, 300);
              }
              setRemoveModalVisible(false);
            }}
          />

          {/* Request Action Confirmation Modal */}
          <GroupConformationModal
            visible={requestAction?.visible}
            header={requestAction?.header}
            content={requestAction?.text}
            optionOneLabel={"Yes"}
            optionTwoLabel={"Cancel"}
            onOptionOne={() => {
              handleRequestAction();
            }}
            loading={loading}
            onOptionTwo={() => {
              if (requestAction?.from === "modal") {
                setTimeout(() => {
                  setModalVisible(true);
                }, 300);
              }
              setRequestAction({
                visible: false,
                text: "",
                header: "",
                data: {},
                from: "",
              });
            }}
          />

          <MoreInfoModal
            listData={
              selectOption?.is_admin === 1
                ? [
                    {
                      id: 2,
                      title: "Remove User",
                    },
                  ]
                : optionMenuData
            }
            visible={isVisible}
            setModalVisible={(e) => setisVisible(e)}
            onBtnPress={(e) => handleToOptionMenu(e)}
          />

          <MoreInfoModal
            listData={selectionAction}
            visible={actionVisible}
            setModalVisible={(e) => {
              setActionVisible(e);
              setTimeout(() => {
                setModalVisible(true);
              }, 300);
            }}
            onBtnPress={(e) => handleAction(e)}
            loader={addLoader}
          />

          {profilePictureModal ? (
            <AlreadyHaveStoryModal
              visible={profilePictureModal}
              setModalVisible={(e) => setProfilePictureModal(e)}
              title1="captureFromCamera"
              title2="chooseFromGallery"
              onPressTitle1={() => setIsCameraOpen(true)}
              onPressTitle2={() => HandleSelectImage()}
            />
          ) : null}
        </>
      )}
    </SafeAreaView>
  );
};

export default GroupInfo;
