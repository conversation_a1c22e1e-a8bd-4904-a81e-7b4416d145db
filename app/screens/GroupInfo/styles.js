import { BaseColors } from "@config/theme";
import { FontFamily } from "@config/typography";

const { StyleSheet, Dimensions } = require("react-native");

const HEIGHT = Dimensions.get("window").height;

const styles = StyleSheet.create({
  main: {
    flex: 1,
    backgroundColor: BaseColors.white,
    marginBottom: 15,
  },
  profileImgMainViewStyle: {
    width: 120,
    height: 120,
    alignItems: "center",
    justifyContent: "center",
    alignSelf: "center",
    marginVertical: 20,
  },
  profileImgMainView: {
    height: 120,
    width: 120,
    borderRadius: 60,
    backgroundColor: "#C4C4C4",
    alignItems: "center",
    justifyContent: "center",
    alignSelf: "center",
  },
  groupHeaderContainer: { justifyContent: "center", alignItems: "center" },
  grouoTitleText: {
    fontSize: 32,
    fontFamily: FontFamily.RobotoBold,
    color: BaseColors.black,
  },
  groupmemberText: {
    fontSize: 18,
    fontFamily: FontFamily.RobotoRegular,
    marginTop: 10,
    color: BaseColors.gray6,
  },
  groupContentContainer: {
    borderWidth: 1,
    borderColor: BaseColors.gray,
    backgroundColor: BaseColors.grayBackgroundColor,
    borderRadius: 8,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    marginHorizontal: 16,
    marginTop: 16,
    marginBottom: 5,
  },
  nobContainer: {
    paddingVertical: 16,
    paddingHorizontal: 16,
  },
  nobText: {
    fontSize: 18,
    fontFamily: FontFamily.RobotoBold,
    color: BaseColors.black,
  },
  nobType: {
    fontSize: 16,
    fontFamily: FontFamily.RobotoRegular,
    marginVertical: 10,
    color: BaseColors.lightBlack10,
  },
  gropSubContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    backgroundColor: BaseColors.backgroundColor,
    borderTopWidth: 1,
    paddingVertical: 10,
    paddingHorizontal: 16,
    borderTopColor: "#eee",
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
    alignItems: "center",
  },
  groupText: {
    fontSize: 18,
    fontFamily: FontFamily.RobotoRegular,
    color: BaseColors.black,
  },
  groupTypeText: {
    fontSize: 19,
    fontFamily: FontFamily.RobotoBold,
    color: BaseColors.activeTab,
  },
  gropAction: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 20,
  },
  gropActionSubContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  groupActionContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginRight: 20,
    borderWidth: 1,
    padding: 7,
    borderRadius: 5,
    backgroundColor: BaseColors.activeTab,
    borderColor: BaseColors.activeTab,
  },
  groupActionText: {
    fontSize: 14,
    fontFamily: FontFamily.RobotSemiBold,
  },
  userRow: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 10,
  },
  avatar: {
    width: 44,
    height: 44,
    borderRadius: 22,
    marginRight: 12,
  },
  info: {
    flex: 1,
  },
  name: {
    fontSize: 15,
    fontWeight: "600",
    color: "#111",
  },
  role: {
    fontSize: 13,
    color: "#555",
    marginTop: 2,
  },
  actions: {
    flexDirection: "row",
    alignItems: "center",
  },
  buttonConiner: {
    paddingHorizontal: 20,
    backgroundColor: BaseColors.white,
  },
  buttonView: {
    width: "48%",
  },
  removeButton: {
    marginHorizontal: 5,
  },
  ovarlayStyle: {
    backgroundColor: "rgba(0,0,0,0.6)",
    flex: 1,
    justifyContent: "flex-end",
  },
  modalView: {
    backgroundColor: BaseColors.white,
    padding: 20,
    borderRadius: 12,
    width: "100%",
    height: "90%",
  },

  modalTitleText: {
    fontSize: 24,
    color: BaseColors.black,
    fontFamily: FontFamily.RobotoMedium,
    textAlign: "center",
    marginBottom: 15,
    textAlignVertical: "center",
  },
  removeBtn: {
    position: "absolute",
    top: 0,
    right: 0,
    backgroundColor: "#DCDFE3",
    borderRadius: 10,
    padding: 1,
  },
  memberImageSize: { width: 55, height: 55, borderRadius: 30 },
  circle: {
    width: 70,
    height: 70,
    borderRadius: 35,
    borderWidth: 1,
    borderColor: BaseColors.activeTab,
    backgroundColor: "rgba(214, 0, 46, 0.2)", // pinkish red
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOpacity: 0.15,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
    elevation: 5,
  },
  addUserContainer: {
    position: "absolute",
    bottom: 50, // Adjust based on tab height
    right: 16,
    zIndex: 999,
  },
  profileImgStyle: {
    width: "100%",
    height: "100%",
    borderRadius: 60,
  },
  modal: {
    justifyContent: "flex-end",
    margin: 0,
  },
  container: {
    backgroundColor: "#fff",
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    paddingVertical: 10,
  },
  option: {
    paddingVertical: 15,
    paddingHorizontal: 20,
    justifyContent: "space-between",
    flexDirection: "row",
    alignItems: "center",
  },
  optionText: {
    fontSize: 16,
    fontWeight: "500",
    color: "#111",
  },
  divider: {
    height: 1,
    backgroundColor: "#e0e0e0",
  },
  cancelButton: {
    marginTop: 8,
    backgroundColor: "#fff",
    borderTopWidth: 1,
    borderColor: "#f0f0f0",
    paddingVertical: 15,
    alignItems: "center",
    borderBottomLeftRadius: 15,
    borderBottomRightRadius: 15,
  },
  cancelText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#007AFF",
  },
  editMainViewStyle: {
    position: "absolute",
    bottom: 0,
    right: 15,
  },
  centerMain: {
    flex: 1,
    marginTop: HEIGHT / 5,
  },
});
export default styles;
