import { getApiData } from "@app/utils/apiHelper";
import BaseSetting from "@config/setting";
import { isEmpty } from "lodash-es";
import Toast from "react-native-simple-toast";

// Block User Chat
export const blockUnBlockUser = async (receiver_id) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.blockUnblock}${receiver_id}`,
      "POST"
    );

    if (resp !== undefined) {
      return resp;
    } else {
      Toast.show(
        resp?.data?.message || "Something went wrong please try again"
      );
    }
  } catch (error) {
    console.error("🚀 ~ likeDislikeToggle ~ error:", error);
  }
};

// clear chat Data
export const clearChatData = async (conversation_id) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.clearChat}${conversation_id}`,
      "POST"
    );

    if (resp !== undefined) {
      return resp;
    } else {
      Toast.show(
        resp?.data?.message || "Something went wrong please try again"
      );
    }
  } catch (error) {
    console.error("🚀 ~ likeDislikeToggle ~ error:", error);
  }
};

// clear chat Data
export const exitGroup = async (group_details_id) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.exitGroup}`,
      "POST",
      {
        group_details_id: group_details_id,
      }
    );
    if (resp?.data?.success) {
      Toast.show(resp?.data?.message);
      return resp?.data;
    } else {
      Toast.show(
        resp?.data?.message || "Something went wrong please try again"
      );
    }
  } catch (error) {
    console.error("🚀 ~ likeDislikeToggle ~ error:", error);
  }
};

// clear chat Data
export const deleteMessage = async (message_id) => {
  try {
    const resp = await getApiData(
      `${BaseSetting.endpoints.deleteMessage}`,
      "POST",
      {
        message_id: message_id,
      }
    );
    if (resp?.data?.success) {
      Toast.show(resp?.data?.message);
      return resp?.data;
    } else {
      Toast.show(
        resp?.data?.message || "Something went wrong please try again"
      );
    }
  } catch (error) {
    console.error("🚀 ~ likeDislikeToggle ~ error:", error);
  }
};

export const sendMultipleImage = async (options, accessToken) => {
  try {
    const authHeaders = {
      "Content-Type": "multipart/form-data",
      authorization: accessToken ? `Bearer ${accessToken}` : "",
    };
    const formData = new FormData();

    // Append receiver_id and message
    options.receiver_id && formData.append("receiver_id", options.receiver_id);
    options.group_details_id &&
      formData.append("group_details_id", options.group_details_id);
    formData.append("message", options.message);

    // Append multiple files
    if (!isEmpty(options?.files)) {
      for (let i = 0; i < options?.files.length; i++) {
        const file = options?.files[i];
        let fileObj;

        // If file is local object with uri
        if (file?.uri) {
          fileObj = {
            uri: file.uri,
            type: file.type || "image/jpeg",
            name: file.name || `image_${i}.jpg`,
          };
        }
        // If file is just filename string from server
        else if (typeof file === "string") {
          fileObj = {
            uri: `https://footbizz.blr1.digitaloceanspaces.com/images/${file}`,
            type: "image/jpeg",
            name: file,
          };
        }

        formData.append("files[]", fileObj);
      }
    }

    const resp = await getApiData(
      `${BaseSetting.endpoints.sendMultipleImage}`,
      "POST",
      formData,
      authHeaders
    );
    console.log("🚀 ~ sendMultipleImage ~ resp:", resp);
    if (resp?.data?.success) {
      return resp?.data;
    } else {
      Toast.show(
        resp?.data?.message || "Something went wrong please try again"
      );
    }
  } catch (error) {
    console.error("🚀 ~ likeDislikeToggle ~ error:", error);
  }
};
