import { translate } from "../../lang/Translate";
import { Platform, Text, TouchableOpacity } from "react-native";
import styles from "./styles";
import Toast from "react-native-simple-toast";
import { signInWithGoogle, signOutGoogle } from "@app/utils/socialLogin";
import { onSubmit } from "@screens/SignUpMobileAndEmail/apiFunctions";
import { handleToSignIn } from "@screens/EmailLoginScreen/apiFunctions";
import authAction from "@redux/reducers/auth/actions";
import AsyncStorage from "@react-native-async-storage/async-storage";
import appleAuth, {
  AppleButton,
} from "@invertase/react-native-apple-authentication";
import { BaseColors } from "@config/theme";
import { facebookService } from "@app/utils/FacebookServices";
import BlockWarningModal from "@components/BlockWarningModal";
import { useState } from "react";
import { useSelector } from "react-redux";

const Animated = require("react-native-reanimated").default;
const Easing = require("react-native-reanimated").Easing;
const FadeInDown = require("react-native-reanimated").FadeInDown;
const FastImage = require("react-native-fast-image");

const {
  setUserData,
  setAccessToken,
  setIsCurrentPlan,
  setCompanyId,
  setUserId,
  setNotificationCount
} = authAction;

const handleToLogin = async (
  id,
  navigation,
  isSignIn,
  dispatch,
  setIsLoading,
  setIsBlockUserModal,
  isBlockUserModal
) => {
  if (Number(id) === 1) {
    googleLogin(
      navigation,
      isSignIn,
      dispatch,
      setIsLoading,
      setIsBlockUserModal
    );
  } else if (Number(id) === 2) {
    appleSignIn(
      navigation,
      isSignIn,
      dispatch,
      setIsLoading,
      setIsBlockUserModal
    );
  } else if (Number(id) === 3) {
    // Toast.show("coming soon...");
    facebookLogin(
      navigation,
      isSignIn,
      dispatch,
      setIsLoading,
      setIsBlockUserModal
    );
  } else if (Number(id) === 4) {
    if (isSignIn) {
      navigation.navigate("EmailWithLogin", { loginType: "mobile" });
    } else {
      navigation.navigate("SignUpWithMobileAndEmail", { signUpType: "mobile" });
    }
  } else if (Number(id === 5)) {
    if (isSignIn) {
      navigation.navigate("EmailWithLogin", { loginType: "email" });
    } else {
      navigation.navigate("SignUpWithMobileAndEmail", { signUpType: "email" });
    }
  }
};

// Facebook Login
const facebookLogin = async (
  navigation,
  isSignIn,
  dispatch,
  setIsLoading,
  setIsBlockUserModal
) => {
  setIsLoading(true);

  // const profileDetails = await signInWithGoogle();
  facebookService.loginToFacebook(async (data) => {
    try {
      if (data?.isCancelled) {
        setIsLoading(false);
      } else {
        let fbAccessToken = data?.accessToken;
        if (fbAccessToken) {
          const data = {
            loginType: "social",
            singUpType: "social",
            idToken: fbAccessToken,
            social_connection: "facebook",
          };
          const response = !isSignIn
            ? await onSubmit(data)
            : await handleToSignIn(data);
          if (response?.data?.success && response?.data?.token) {
            if (response?.data?.data?.is_completed === 0) {
              dispatch(setCompanyId(response?.data?.data?.company_id));
              dispatch(setUserId(response?.data?.data?.user_id));
            }
            // navigation.navigate("SingUpWithFastRegistrationSecond", {
            //   user_id: response?.data?.data?.user_id,
            //   company_id: response?.data?.data?.company_id,
            // });
            // Toast.show(
            //   "Ohh, we can see here you are not full fill your company detail"
            // );
            // setIsLoading(false);
            // } else {
              if(response?.data?.data?.notification_count){
                dispatch(setNotificationCount(Number(response?.data?.data?.notification_count)));
              }
            dispatch(setUserData(response?.data?.data));
            dispatch(setAccessToken(response?.data?.token));
            AsyncStorage.setItem("token", response?.data?.token);
            if (response?.data?.data?.active_plan?.length !== 0) {
              dispatch(setIsCurrentPlan(response?.data?.data?.active_plan));
            }
            navigation.replace("HomeScreen");
            Toast.show(response?.data?.message);
            setIsLoading(false);
            // }
          } else if (response?.data?.success) {
            navigation.navigate("SignUpWithFastRegistration", {
              data: response?.data,
            });
            setIsLoading(false);
          } else {
            setIsLoading(false);
            if (response?.data?.is_banned === true) {
              setIsBlockUserModal(true);
            } else {
              Toast.show(response?.data?.message);
            }
          }
          setIsLoading(false);
        }
      }
    } catch (error) {
      console.log("🚀 ~ facebookService.loginToFacebook ~ error:", error);
      setIsLoading(false);
    }
  });
};

// Google Login and Singup functionality
const googleLogin = async (
  navigation,
  isSignIn,
  dispatch,
  setIsLoading,
  setIsBlockUserModal
) => {
  setIsLoading(true);
  try {
    const profileDetails = await signInWithGoogle();
    signOutGoogle();

    const data = {
      loginType: "social",
      singUpType: "social",
      idToken: profileDetails?.idToken,
      social_connection: "google",
    };
    const response = !isSignIn
      ? await onSubmit(data)
      : await handleToSignIn(data);

    console.log("Your Account detail is :", response?.data);
    if (response?.data?.success && response?.data?.token) {
      if (response?.data?.data?.is_completed === 0) {
        dispatch(setCompanyId(response?.data?.data?.company_id));
        dispatch(setUserId(response?.data?.data?.user_id));
      }
      // navigation.navigate("SingUpWithFastRegistrationSecond", {
      //   user_id: response?.data?.data?.user_id,
      //   company_id: response?.data?.data?.company_id,
      // });
      // Toast.show(
      //   "Ohh, we can see here you are not full fill your company detail"
      // );
      // setIsLoading(false);
      // } else {
        if(response?.data?.data?.notification_count){
          dispatch(setNotificationCount(Number(response?.data?.data?.notification_count)));
        }
      dispatch(setUserData(response?.data?.data));
      dispatch(setAccessToken(response?.data?.token));
      AsyncStorage.setItem("token", response?.data?.token);
      if (response?.data?.data?.active_plan?.length !== 0) {
        dispatch(setIsCurrentPlan(response?.data?.data?.active_plan));
      }
      navigation.replace("HomeScreen");
      Toast.show(response?.data?.message);
      setIsLoading(false);
      // }
    } else if (response?.data?.success) {
      navigation.navigate("SignUpWithFastRegistration", {
        data: response?.data,
      });
      setIsLoading(false);
    } else {
      setIsLoading(false);
      if (response?.data?.is_banned === true) {
        setIsBlockUserModal(true);
      } else {
        Toast.show(response?.data?.message);
      }
    }
    setIsLoading(false);
  } catch (error) {
    if (error.code === "-5") {
      setIsLoading(false);
      Toast.show("Google login cancel by user");
    } else {
      setIsLoading(false);
      Toast.show(error.toString());
    }
  }
};

const appleSignIn = async (
  navigation,
  isSignIn,
  dispatch,
  setIsLoading,
  setIsBlockUserModal
) => {
  setIsLoading(true);
  try {
    const appleAuthRequestResponse = await appleAuth.performRequest({
      requestedOperation: appleAuth.Operation.LOGIN,
      requestedScopes: [appleAuth.Scope.EMAIL, appleAuth.Scope.FULL_NAME],
    });

    // get current authentication state for user
    // /!\ This method must be tested on a real device. On the iOS simulator it always throws an error.
    const credentialState = await appleAuth.getCredentialStateForUser(
      appleAuthRequestResponse.user
    );

    // use credentialState response to ensure the user is authenticated
    if (credentialState === appleAuth.State.AUTHORIZED) {
      const data = {
        loginType: "social",
        singUpType: "social",
        idToken: appleAuthRequestResponse.identityToken,
        social_connection: "apple",
      };
      if (
        appleAuthRequestResponse?.fullName?.familyName !== null &&
        appleAuthRequestResponse?.fullName?.givenName !== null
      ) {
        data.tname =
          appleAuthRequestResponse?.fullName?.givenName +
          " " +
          appleAuthRequestResponse?.fullName?.familyName;
      }
      const response = !isSignIn
        ? await onSubmit(data)
        : await handleToSignIn(data);
      if (response?.data?.success && response?.data?.token) {
        if (response?.data?.data?.is_completed === 0) {
          dispatch(setCompanyId(response?.data?.data?.company_id));
          dispatch(setUserId(response?.data?.data?.user_id));
        }
        // navigation.navigate("SingUpWithFastRegistrationSecond", {
        //   user_id: response?.data?.data?.user_id,
        //   company_id: response?.data?.data?.company_id,
        // });
        // Toast.show(
        //   "Ohh, we can see here you are not full fill your company detail"
        // );
        // setIsLoading(false);
        // } else {
          if(response?.data?.data?.notification_count){
            dispatch(setNotificationCount(Number(response?.data?.data?.notification_count)));
          }
        dispatch(setUserData(response?.data?.data));
        dispatch(setAccessToken(response?.data?.token));
        AsyncStorage.setItem("token", response?.data?.token);
        if (response?.data?.data?.active_plan?.length !== 0) {
          dispatch(setIsCurrentPlan(response?.data?.data?.active_plan));
        }
        navigation.replace("HomeScreen");
        Toast.show(response?.data?.message);
        setIsLoading(false);
        // }
      } else if (response?.data?.success) {
        navigation.navigate("SignUpWithFastRegistration", {
          data: response?.data,
        });
        setIsLoading(false);
      } else {
        setIsLoading(false);
        if (response?.data?.is_banned === true) {
          setIsBlockUserModal(true);
        } else {
          Toast.show(response?.data?.message);
        }
      }
      setIsLoading(false);
    }
  } catch (error) {
    if (error.code === "-5") {
      setIsLoading(false);

      Toast.show("Google login cancel by user");
    } else {
      setIsLoading(false);
      console.log("Your Google Login error", error);
      Toast.show("Sign in cancelled by user");
    }
  }
};

export const RenderItem = ({
  item,
  navigation,
  index,
  isSignIn,
  dispatch,
  setIsLoading,
}) => {
  const [isBlockUserModal, setIsBlockUserModal] = useState(false);
  const { adminSettingData } = useSelector((state) => state.auth);
  return (
    <Animated.View entering={FadeInDown.duration(400).easing(Easing.ease)}>
      {Platform.OS === "ios" && index === 0 ? (
        <AppleButton
          buttonStyle={AppleButton.Style.WHITE_OUTLINE}
          buttonType={
            isSignIn ? AppleButton.Type.SIGN_IN : AppleButton.Type.SIGN_UP
          }
          style={{
            height: 45, // You must specify a height
            borderColor: BaseColors.gray,
            marginHorizontal: 20,
          }}
          onPress={() =>
            handleToLogin(
              item.id,
              navigation,
              isSignIn,
              dispatch,
              setIsLoading,
              setIsBlockUserModal
            )
          }
        />
      ) : (
        <TouchableOpacity
          style={styles.renderItemMainView}
          activeOpacity={0.8}
          onPress={() => {
            handleToLogin(
              item.id,
              navigation,
              isSignIn,
              dispatch,
              setIsLoading,
              setIsBlockUserModal
            );
          }}
        >
          <FastImage
            source={item.img}
            style={styles.iconStyle}
            resizeMode="contain"
          />
          <Text style={styles.itemText}>{translate(item.text)}</Text>
        </TouchableOpacity>
      )}
      {isBlockUserModal ? (
        <BlockWarningModal
          visible={isBlockUserModal}
          setModalVisible={(e) => setIsBlockUserModal(e)}
          text={"contactAdminTxt"}
          textTitle={
            adminSettingData?.find((obj) => obj?.slug === "CONTACTEMAIL")?.value
          }
        />
      ) : null}
    </Animated.View>
  );
};
