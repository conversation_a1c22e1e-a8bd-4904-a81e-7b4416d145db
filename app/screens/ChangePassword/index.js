import React, { memo, useCallback, useMemo, useRef, useState } from "react";
import {
  KeyboardAvoidingView,
  SafeAreaView,
  ScrollView,
  Text,
  View,
} from "react-native";
import styles from "./styles";
import CInput from "@components/TextInput";
import CButton from "@components/CButton";
import CHeader from "@components/CHeader";
import { translate } from "../../lang/Translate";
import * as yup from "yup";
import { Controller, useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { onsubmit } from "./apiFunctions";
import { BaseColors } from "@config/theme";
import { useFocusEffect } from "@react-navigation/native";
import ErrorComponent from "@components/ErrorComponent";
import isEmpty from "lodash-es/isEmpty";
import Toast from "react-native-simple-toast";

const Animated = require("react-native-reanimated").default;
const FadeInDown = require("react-native-reanimated").FadeInDown;

// password schema validation
const NewPasswordSchema = yup.object().shape({
  old_Password: yup.string().required("enterOldPsd"),
  new_password: yup
    .string()
    .required("enterPsd")
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&_])[A-Za-z\d@$!%*?&_]{8,}$/,
      "PasswordsMustBe"
    ),
  reEnter_password: yup
    .string()
    .required("reEnterPsd")
    .oneOf([yup.ref("new_password"), null], "passwordsMatch"),
});

const ChangePassword = ({ navigation, route }) => {
  // ref's
  const oldPassRef = useRef(null);
  const newPassRef = useRef(null);
  const reenterPassRef = useRef(null);
  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(NewPasswordSchema),
  });

  // state
  const [isLoading, setIsLoading] = useState(false);
  const [errorMsg, seterrorMsg] = useState("");

  // memo's
  const isLoadingMemo = useMemo(() => isLoading, [isLoading]);

  // submit function
  const handleToSubmit = async (e) => {
    setIsLoading(true);
    const resp = await onsubmit(e, setIsLoading);

    if (resp?.data?.success) {
      setIsLoading(false);
      seterrorMsg("");
      navigation.goBack();
      Toast.show("Password update successfully");
    } else {
      seterrorMsg(resp?.data?.message);
      setIsLoading(false);
    }
  };

  // from reset function
  useFocusEffect(
    useCallback(() => {
      reset();
      seterrorMsg("");
    }, [])
  );

  return (
    <SafeAreaView style={styles.mainView}>
      {/* header  */}
      <CHeader
        handleBackButton={() => navigation.goBack()}
        headingTitle="changePassword"
      />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : null}
        style={{
          flex: 1,
          backgroundColor: BaseColors.white,
        }}
      >
        <ScrollView>
          <View style={styles.contentView}>
            <Animated.View entering={FadeInDown} style={styles.cInputStyle}>
              {!isEmpty(errorMsg) ? (
                <ErrorComponent
                  text={errorMsg}
                  onPressClose={() => seterrorMsg("")}
                />
              ) : null}
              <Controller
                control={control}
                rules={{
                  required: true,
                }}
                name="old_Password"
                render={({ field: { onChange, value } }) => (
                  <CInput
                    reference={oldPassRef}
                    placeholderText={translate("OldPassword")}
                    rightIconSize={18}
                    returnKeyType="next"
                    value={value}
                    onChange={onChange}
                    onSubmit={() => {
                      newPassRef.current.focus();
                    }}
                    passwordInputField={true}
                    isError={errors?.old_Password}
                    isErrorMsg={errors?.old_Password?.message}
                  />
                )}
              />
            </Animated.View>
            <Animated.View entering={FadeInDown} style={styles.cInputStyle}>
              <Controller
                control={control}
                rules={{
                  required: true,
                }}
                name="new_password"
                render={({ field: { onChange, value } }) => (
                  <CInput
                    reference={newPassRef}
                    placeholderText={translate("CreatePassword")}
                    rightIconSize={18}
                    returnKeyType="next"
                    value={value}
                    onChange={onChange}
                    onSubmit={() => {
                      reenterPassRef.current.focus();
                    }}
                    passwordInputField={true}
                    isError={errors?.new_password}
                    isErrorMsg={errors?.new_password?.message}
                  />
                )}
              />
            </Animated.View>
            <Animated.View entering={FadeInDown} style={styles.cInputStyle}>
              <Controller
                control={control}
                rules={{
                  required: true,
                }}
                name="reEnter_password"
                render={({ field: { onChange, value } }) => (
                  <CInput
                    reference={reenterPassRef}
                    placeholderText={translate("ConfirmPassword")}
                    passwordInputField={true}
                    returnKeyType="next"
                    value={value}
                    onChange={onChange}
                    onSubmit={handleSubmit(handleToSubmit)}
                    isError={errors?.reEnter_password}
                    isErrorMsg={errors?.reEnter_password?.message}
                  />
                )}
              />
            </Animated.View>
            <View style={styles.PsdText}>
              <Text style={{ color: BaseColors.gray11 }}>
                {translate("PasswordsMustBe")}
              </Text>
            </View>

            <CButton
              style={styles.btnStyle}
              onBtnClick={handleSubmit((e) => handleToSubmit(e))}
              loading={isLoadingMemo}
              seterrorMsg={seterrorMsg}
            >
              {translate("Update")}
            </CButton>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};
export default memo(ChangePassword);
