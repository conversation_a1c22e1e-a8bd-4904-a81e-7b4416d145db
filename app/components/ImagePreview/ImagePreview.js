import { CustomIcon } from "@config/LoadIcons";
import { BaseColors } from "@config/theme";
import React from "react";
import {
  Modal,
  FlatList,
  Image,
  Dimensions,
  View,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
} from "react-native";

const { width, height } = Dimensions.get("window");

export default function ImagePreviewExample({ images, startIndex, onClose }) {
  console.log(
    "🚀 ~ ImagePreviewExample ~ images, startIndex, onClose:",
    images,
    startIndex,
    onClose
  );
  if (!Array.isArray(images) || images.length === 0) return null;

  return (
    <Modal animationType="slide" transparent={true} visible>
      <FlatList
        data={images}
        horizontal
        pagingEnabled
        initialScrollIndex={startIndex}
        keyExtractor={(item, index) => index.toString()}
        getItemLayout={(data, index) => ({
          length: width,
          offset: width * index,
          index,
        })}
        renderItem={({ item }) => (
          <View style={styles.imageWrapper}>
            <Image
              source={{ uri: item }}
              style={styles.fullImage}
              resizeMode="contain"
            />
          </View>
        )}
      />
      <TouchableOpacity style={styles.closeButton} onPress={onClose}>
        <View style={styles.closeCircle}>
          <CustomIcon name="BsX" size={20} color={BaseColors.black} />
        </View>
      </TouchableOpacity>
    </Modal>
  );
}

const styles = StyleSheet.create({
  imageWrapper: {
    width,
    height,
    backgroundColor: "black",
    justifyContent: "center",
    alignItems: "center",
  },
  fullImage: {
    width,
    height,
  },
  closeButton: {
    position: "absolute",
    top: 40,
    right: 20,
  },
  closeCircle: {
    width: 30,
    height: 30,
    backgroundColor: "white",
    borderRadius: 15,
    justifyContent: "center",
    alignItems: "center",
  },
  mainView: {
    flex: 1,
    justifyContent: "flex-end",
    backgroundColor: "hsla(360, 20%,2%, 0.6)",
    // paddingBottom: 20,
    paddingTop: "50%",
  },
  background: {
    backgroundColor: BaseColors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingVertical: 20,
    elevation: 3,
  },
});
