import { StyleSheet } from "react-native";

const styles = StyleSheet.create({
  row: {
    flexDirection: "row",
    alignItems: "center",
  },
  errorTxt: {
    color: "#FF0B1E",
    paddingLeft: 5,
    textAlign: "left",
    marginBottom: -8,
  },
  dropdown: {
    flex: 1,
    borderWidth: 1,
    color: "#555454",
    borderTopRightRadius: 5,
    borderTopLeftRadius: 5,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    paddingHorizontal: 8,
    paddingLeft: 20,
  },
  placeholderStyle: {
    fontSize: 16,
    color: "#555454",
    // fontFamily: FontFamily.medium,
  },
  selectedTextStyle: {
    fontSize: 16,
    color: "#000000",
    // fontFamily: FontFamily.medium,
    textTransform: "capitalize",
  },
  iconStyle: {
    width: 20,
    height: 20,
  },
  inputSearchStyle: {
    height: 40,
    fontSize: 16,
    color: "#000000",
  },
  button: {
    marginHorizontal: 16,
  },
  selectedStyle: {
    borderBottomRightRadius: 5,
    borderBottomLeftRadius: 5,
    borderTopRightRadius: 5,
    borderTopLeftRadius: 5,
    backgroundColor: "#F1F2F4",
    borderColor: "#8E8383",
    borderWidth: 0.5,
  },
  container: {
    position: "relative",
  },
  errorTxt: {
    color: "#D6002E",
    textAlign: "left",
  },
  errorMsgMainView: {
    flexDirection: "row",
    alignItems: "center",
    paddingTop: 8,
    gap: 8,
  },
  selectRow: {
    padding: 15,
    display: "flex",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  buttonText: {
    fontSize: 16,
    color: "#555454",
    // fontFamily: FontFamily.medium,
  },
  renderItemDataView: {
    borderWidth: 1,
    flexDirection: "row",
    padding: 6,
    marginRight: 2,
    marginVertical: 5,
    borderRadius: 5,
    backgroundColor: "rgba(241, 242, 244, 1)",
    borderColor: "rgba(241, 242, 244, 1)",
  },
});

export default styles;
