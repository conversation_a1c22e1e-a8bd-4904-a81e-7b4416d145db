/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react-native/no-inline-multiStyles */
import React, {
  useState,
  useImperativeHandle,
  forwardRef,
  useEffect,
  useRef,
  memo,
  useLayoutEffect,
} from "react";
import {
  Dimensions,
  Text,
  TouchableOpacity,
  View,
  Keyboard,
  FlatList,
  Modal,
  TouchableWithoutFeedback,
  I18nManager,
  Platform,
  TextInput,
} from "react-native";
import PropTypes from "prop-types";
import _ from "lodash";
import Animated, {
  interpolate,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  Easing,
} from "react-native-reanimated";
import { BaseColors } from "@config/theme";
import multiStyles from "./multiStyles";
import { CustomIcon } from "@config/LoadIcons";
import Checkbox from "@components/Checkbox";
import { FontFamily } from "@config/typography";
import { translate } from "../../lang/Translate";

const CMultiDropdown = memo(
  forwardRef((props, ref) => {
    const {
      data = [],
      disabled = false,
      onPress = () => {},
      value = [],
      placeholder = "",
      style = {},
      textStyle = {},
      onChange = () => {},
      setSelected = () => {},
      listProps = "title",
      showError = false,
      errorMsg = "",
      ListEmptyComponent = () => {},
      listEmptyText = "No data found",
      valueProp = "title",
      label = "",
      labelTextStyle = {},
      required = false,
      multiple = false,
      listPosition = "top",
      onEndReached = () => {},
      setItem = () => {},
      setValue = () => {},
      dropDownName = "",
      setCountryIds,
      search = true,
      nature_of_business,
    } = props;

    const rootRef = useRef();
    const IOS = Platform.OS === "ios";
    const valueRef = useRef();
    const listRef = useRef();
    const [showList, setShowList] = useState(false);
    const [optionArr, setOptionArr] = useState([]);
    const [searchQuery, setSearchQuery] = useState("");
    const [selectedIDs, setSelectedIds] = useState(data);
    const [position, setPosition] = useState({});

    useEffect(() => {
      setSelectedIds(data);
    }, [data]);

    useImperativeHandle(ref, () => ({
      listClose() {
        setShowList(false);
      },
      getLabel() {
        const filterValue = multiple
          ? data?.filter((v) => value?.includes(v[label]))
          : data?.find((v) => value == v[label]);
        const title =
          !multiple && _.isObject(filterValue) ? filterValue[listProps] : "";

        return title;
      },
    }));

    useEffect(() => {
      if (_.isArray(data) && !_.isEmpty(data)) {
        if (_.isEmpty(optionArr) || _.isEmpty(searchQuery)) {
          setOptionArr(data);
        }
      }
    }, [data, showList]);

    useLayoutEffect(() => {
      checkViewPosition(false);
    }, [data, showList]);

    const checkViewPosition = (bool = true) => {
      if (rootRef?.current) {
        rootRef.current.measureInWindow((pageX, pageY, width, height) => {
          const H = Dimensions.get("window").height;
          const W = Dimensions.get("window").width;
          const viewHeight = 50;
          const top = height + pageY;
          const bottom = H - height - pageY + viewHeight;
          const left = I18nManager.isRTL ? W - width - pageX : pageX;
          setPosition({
            width: Math.floor(width),
            top: Math.floor(top),
            bottom: Math.floor(bottom),
            left: Math.floor(left),
          });
          bool && setShowList(!showList);
        });
      }
    };

    const onSearch = (val) => {
      setSearchQuery(val);
      const value = val?.toLowerCase();
      const optionList = val
        ? data.filter((item) => item["label"]?.toLowerCase()?.includes(value))
        : data;
      setOptionArr([...optionList]);
    };

    const onLayout = (event) => {
      const { x, y } = event.nativeEvent.layout;
    };

    const rotation = useSharedValue(0);

    const rotateUp = () => {
      rotation.value = withTiming(180, {
        duration: 200,
        easing: Easing.linear,
      });
    };
    const rotateDown = () => {
      rotation.value = withTiming(0, {
        duration: 200,
        easing: Easing.linear,
      });
    };

    const onRequestClose = () => {
      setShowList(false);
      rotateDown();
    };

    // Function to toggle the accordion by animating the shared value
    const toggleButton = () => {
      if (rotation.value === 0) {
        rotateUp();
        setSearchQuery("");
      } else {
        rotateDown();
      }
    };

    const iconStyle = useAnimatedStyle(() => {
      return {
        transform: [
          {
            rotate: `${interpolate(rotation.value, [0, 180], [0, 180])}deg`,
          },
        ],
      };
    });

    const renderDropDown = () => {
      const filterValue = multiple
        ? data?.filter((v) => value?.includes(v["value"]))
        : data?.find((v) => value == v["value"]);
      const title =
        !multiple && _.isObject(filterValue) ? filterValue[listProps] : "";

      const removeSelectedItem = (sleetedID) => {
        const d = value.includes(sleetedID);
        if (d) {
          const flt = value.filter((s) => s !== sleetedID);
          setSelected(flt);
          setItem(flt);
        } else {
          const t = [...value];
          t.push(sleetedID);
          setSelected(t);
          setItem(t);
        }
      };

      return (
        <View ref={rootRef} onLayout={onLayout}>
          {/* Label container here */}
          {label ? (
            <View style={[multiStyles.labelContainer]}>
              <View style={multiStyles.labelAndRequiredIconContainer}>
                <Text style={[labelTextStyle]}>{label}</Text>
              </View>
            </View>
          ) : null}

          <TouchableOpacity
            activeOpacity={0.7}
            disabled={disabled}
            onPress={() => {
              Keyboard?.dismiss();
              setTimeout(checkViewPosition, IOS ? 100 : 0);
              toggleButton();
              onPress();
            }}
            style={multiStyles.mainContainer}
          >
            {multiple ? (
              <FlatList
                ref={valueRef}
                horizontal
                showsHorizontalScrollIndicator={false}
                data={filterValue}
                nestedScrollEnabled
                renderItem={({ item, index }) => {
                  return (
                    <>
                      <TouchableOpacity
                        activeOpacity={0.8}
                        disabled={disabled}
                        key={`selected_${index}`}
                        onPress={() => removeSelectedItem(item?.value)}
                        style={{
                          paddingVertical: 10,
                        }}
                      >
                        <View
                          style={{
                            borderWidth: 1,
                            padding: 6,
                            marginRight: 8,
                            marginVertical: 5,
                            borderRadius: 5,
                            backgroundColor: "rgba(241, 242, 244, 1)",
                            borderColor: "rgba(241, 242, 244, 1)",
                          }}
                        >
                          <Text
                            style={{
                              color: BaseColors.black100,
                              fontSize: 16,
                              fontFamily: FontFamily.medium,
                              marginBottom: 3,
                              textTransform: "capitalize",
                            }}
                          >
                            {item?.label}
                          </Text>
                          <View
                            style={{
                              position: "absolute",
                              right: -10,
                              top: -10,
                              zIndex: 999,
                            }}
                          >
                            <CustomIcon
                              name="BsX"
                              size={22}
                              color={BaseColors.black}
                            />
                          </View>
                        </View>
                      </TouchableOpacity>
                    </>
                  );
                }}
                ListEmptyComponent={() => {
                  return (
                    <Text
                      numberOfLines={1}
                      style={[multiStyles.placeholderText, textStyle]}
                    >
                      {translate(placeholder)}
                    </Text>
                  );
                }}
              />
            ) : (
              <Text
                numberOfLines={1}
                style={[
                  multiStyles.placeholderText,
                  {
                    width: "90%",
                    color: title
                      ? BaseColors.inputColor
                      : BaseColors.placeholder,
                  },
                  textStyle,
                ]}
              >
                {title ? title : placeholder}
              </Text>
            )}
            {/* Arrow Button Container */}
            <View style={[multiStyles.iconContainer]}>
              <Animated.View
                style={[multiStyles.arrowIconContainer, iconStyle]}
              >
                <CustomIcon
                  name={"BsChevronDown"}
                  size={20}
                  color={BaseColors.gray2}
                />
              </Animated.View>
              {/* {showError && (
                <Icon
                  type={Icons.MaterialIcons}
                  name="warning"
                  size={18}
                color={BaseColors.red}
                style={[{marginEnd: 10}]}
                />
              )} */}
            </View>
          </TouchableOpacity>
        </View>
      );
    };

    const RenderDropDownItems = React.memo(({ item, index }) => {
      const title = !_.isEmpty(listProps) ? item[listProps] : item?.name;
      const isSelected = multiple
        ? value?.some((v) => v == item[valueProp])
        : value == item[valueProp];

      const isLast = index == optionArr?.length - 1;
      const isFirst = index == 0;
      return (
        <TouchableOpacity
          activeOpacity={0.7}
          key={`dropDown_${title}_${index + 1}`}
          style={[
            multiStyles.dropDownList,
            !multiple &&
              isFirst && {
                borderTopRightRadius: 3,
                borderTopLeftRadius: 3,
              },
            isLast
              ? {
                  borderBottomLeftRadius: 3,
                  borderBottomRightRadius: 3,
                }
              : {
                  borderBottomWidth: 1,
                },
            {
              backgroundColor: nature_of_business
                ? BaseColors.white
                : isSelected
                  ? BaseColors.primary
                  : BaseColors.white,
            },
          ]}
          onPress={() => {
            const d = value.includes(
              nature_of_business
                ? item?.nature_of_business_id
                : item?.id || item?.value
            );
            console.log(
              "daa======>",
              item,
              item?.value === value ? true : false
            );
            if (d) {
              const flt = value.filter(
                (s) =>
                  s !==
                  (nature_of_business
                    ? item?.nature_of_business_id
                    : item?.id || item?.value)
              );
              setSelected(flt);
              setItem(flt);
              setSelectedIds(flt);
            } else {
              const t = [...value];
              t.push(
                nature_of_business
                  ? item?.nature_of_business_id
                  : item?.id || item?.value
              );
              setSelected(t);
              setItem(t);
              setSelectedIds(t);
            }
          }}
        >
          <View style={multiStyles.selectRow}>
            <Text style={multiStyles.buttonText}>{item.label}</Text>
            <Checkbox
              isChecked={value.includes(
                nature_of_business
                  ? item?.nature_of_business_id
                  : item?.id || item?.value
              )}
              toggleCheckbox={() => {
                const d = value.includes(
                  nature_of_business
                    ? item?.nature_of_business_id
                    : item?.id || item?.value
                );
                console.log(
                  "daa======>",
                  item,
                  item?.value === value ? true : false
                );
                if (d) {
                  const flt = value.filter(
                    (s) =>
                      s !==
                      (nature_of_business
                        ? item?.nature_of_business_id
                        : item?.id || item?.value)
                  );
                  setSelected(flt);
                  setItem(flt);
                } else {
                  const t = [...value];
                  t.push(
                    nature_of_business
                      ? item?.nature_of_business_id
                      : item?.id || item?.value
                  );
                  setSelected(t);
                  console.log("🚀 ~ RenderDropDownItems ~ t:", t);

                  setItem(t);
                }
              }}
            />
          </View>
        </TouchableOpacity>
      );
    });

    return (
      <View style={[multiStyles.root, style]}>
        {renderDropDown()}
        {showError ? (
          <View style={multiStyles.errorMsgMainView}>
            <CustomIcon
              name={"BsExclamationCircle"}
              size={18}
              color={"#D6002E"}
            />
            <Text style={multiStyles.errorTxt}>{translate(errorMsg)}</Text>
          </View>
        ) : null}
        <Modal transparent visible={showList} onRequestClose={onRequestClose}>
          <TouchableWithoutFeedback onPress={onRequestClose}>
            <View style={[{ flex: 1 }]}>
              <View
                style={[
                  multiStyles.mainListContainer,
                  { left: position?.left },
                  listPosition === "bottom"
                    ? { bottom: position?.bottom }
                    : { top: position?.top },
                ]}
              >
                <TouchableOpacity
                  activeOpacity={1}
                  style={[
                    multiStyles.listContainer,
                    {
                      width: position?.width,
                      height:
                        optionArr?.length > 5
                          ? Dimensions.get("screen").height / 3
                          : null,
                    },
                  ]}
                >
                  {search && (
                    <View
                      style={{
                        flexDirection: "row",
                        alignItems: "center",
                      }}
                    >
                      <TextInput
                        placeholder="Search..."
                        value={searchQuery}
                        onChangeText={onSearch}
                        style={{
                          borderColor: "#555454",
                          borderWidth: 0.8,
                          borderRadius: 5,
                          margin: 10,
                          height: 40,
                          width: "67%",
                          paddingLeft: 8,
                        }}
                      />

                      <View style={{ flexDirection: "row", gap: 5 }}>
                        <Text>Select All</Text>
                        <Checkbox
                          isChecked={
                            selectedIDs?.length === data?.length ? true : false
                          }
                          toggleCheckbox={() => {
                            if (selectedIDs?.length === data?.length) {
                              setValue(dropDownName, []);
                              if (dropDownName == "SelectedCountry") {
                                setCountryIds([]);
                              }
                              setSelectedIds([]);
                              setSearchQuery("");
                            } else {
                              const valuesArray = data.map(
                                (item) => item.value
                              );
                              setSearchQuery("");
                              setValue(dropDownName, valuesArray);
                              if (dropDownName == "SelectedCountry") {
                                setCountryIds(valuesArray);
                              }
                              setSelectedIds(data);
                            }
                          }}
                        />
                      </View>
                    </View>
                  )}
                  <FlatList
                    ref={listRef}
                    data={optionArr || []}
                    nestedScrollEnabled
                    keyExtractor={(item) => item[valueProp] || item?.id}
                    contentContainerStyle={{ flexGrow: 1 }}
                    renderItem={({ item, index }) => (
                      <RenderDropDownItems item={item} index={index} />
                    )}
                    onEndReachedThreshold={0.5}
                    onEndReached={onEndReached}
                    ListEmptyComponent={() => {
                      if (!_.isUndefined(ListEmptyComponent())) {
                        ListEmptyComponent();
                      } else {
                        return (
                          <View style={[multiStyles.dropDownEmptyContainer]}>
                            <Text style={[multiStyles.listEmptyText]}>
                              {listEmptyText}
                            </Text>
                          </View>
                        );
                      }
                    }}
                  />
                </TouchableOpacity>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </Modal>
      </View>
    );
  })
);

CMultiDropdown.propTypes = {
  data: PropTypes.array,
  disabled: PropTypes.bool,
  onPress: PropTypes.func,
  value: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
    PropTypes.array,
  ]),
  placeholder: PropTypes.string,
  style: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
  textStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
  label: PropTypes.string,
  onChange: PropTypes.func,
  listProps: PropTypes?.string,
  errorMsg: PropTypes.string,
  ListEmptyComponent: PropTypes.func,
  showError: PropTypes.bool,
  listEmptyText: PropTypes.string,
  isSearch: PropTypes.bool,
  valueProp: PropTypes.string,
  labelTextStyle: PropTypes.object,
  required: PropTypes.bool,
  multiple: PropTypes.bool,
  listPosition: PropTypes.string,
  onEndReached: PropTypes.func,
};

export default CMultiDropdown;
