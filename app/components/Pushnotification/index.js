/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect } from "react";
import { View } from "react-native";
import { useDispatch, useSelector } from "react-redux";
// import messaging from "@react-native-firebase/messaging";
import BaseSetting from "@config/setting";
import AuthActions from "@redux/reducers/auth/actions";
import socketAction from "@redux/reducers/socket/actions";
import { getApiData } from "../../utils/apiHelper";
import isEmpty from "lodash-es/isEmpty";
import isObject from "lodash-es/isObject";
import { withInAppNotification } from "../../libs/react-native-in-app-notification";
import { navigationRef } from "@navigation/NavigationService";

const { images } = require("@config/images");
const messaging = require("@react-native-firebase/messaging").default;

const { setTotalMsgCount } = socketAction;

/**
 *
 *@module PushNotification
 *
 */
const PushNotification = (props) => {
  const {
    setUUid,
    setSavedPostList,
    setSavedReelList,
    setNotificationActivity,
    setNotificationCount,
    setFeedBackModal,
  } = AuthActions;
  const dispatch = useDispatch();
  const { uuid, userData, accessToken } = useSelector((state) => state.auth);
  const { selectedRoom } = useSelector((state) => state.socket);
  const currentStack = navigationRef.current.getCurrentRoute();

  useEffect(() => {
    if (isObject(userData) && !isEmpty(userData)) {
      checkNotificationPermission();
    }
  }, [props, userData, accessToken]);

  // this function for check notification permission
  async function checkNotificationPermission() {
    const hasPermission = await messaging().hasPermission();
    try {
      const enabled =
        hasPermission === messaging.AuthorizationStatus.AUTHORIZED ||
        hasPermission === messaging.AuthorizationStatus.PROVISIONAL;

      if (!enabled) {
        const authorizationStatus = await messaging().requestPermission();
        if (authorizationStatus === messaging.AuthorizationStatus.AUTHORIZED) {
          console.log("User has notification permissions enabled.");
        } else if (
          authorizationStatus === messaging.AuthorizationStatus.PROVISIONAL
        ) {
          console.log("User has provisional notification permissions.");
        } else {
          console.log("User has notification permissions disabled");
        }
      }
      if (!uuid && isObject(userData) && !isEmpty(userData)) {
        getFcmToken();
      }
    } catch (error) {
      console.log("checkApplicationPermission -> error", error);
    }
  }

  // this function for send token to server
  /** this function for send token to server
   * @function sendFcmToken
   * @param {object} data {}
   */
  async function sendFcmToken(token) {
    const data = {
      fcm: token,
    };
    try {
      const response = await getApiData(
        BaseSetting.endpoints.sendFcmToken,
        "POST",
        data,
        {
          "Content-Type": "application/json",
          authorization: accessToken ? `Bearer ${accessToken}` : "",
        }
      );
    } catch (err) {
      console.log("ERRR==", err);
    }
  }

  // this function for get firebase token
  async function getFcmToken() {
    const fcmToken = await messaging()
      .getToken()
      .catch((e) => {
        console.log("called====3Dddd", e);
      });
    if (fcmToken && accessToken) {
      sendFcmToken(fcmToken);
      dispatch(setUUid(fcmToken));
    }
  }

  const handleNavigation = (selectedItemData) => {
    if (selectedItemData?.notificationType === "post") {
      if (selectedItemData?.postData && !isEmpty(selectedItemData?.postData)) {
        const updatedPostList = {
          page: 1,
          next_enable: false,
          data: [selectedItemData?.postData],
        };
        dispatch(setSavedPostList(updatedPostList));

        navigationRef.current.navigate("SavedPostList", {
          data: [selectedItemData?.postData],
          screenName: "notification",
          notificationType: "post",
        });
      }
    } else if (selectedItemData?.notificationType === "comment") {
      if (selectedItemData?.postData && !isEmpty(selectedItemData?.postData)) {
        const updatedPostList = {
          page: 1,
          next_enable: false,
          data: [selectedItemData?.postData],
        };
        if (selectedItemData?.post_type === "post") {
          dispatch(setSavedPostList(updatedPostList));
          navigationRef.current.navigate("SavedPostList", {
            data: updatedPostList,
            screenName: "notification",
            notificationType: "comment",
            commentPostId: selectedItemData?.postData?.post_id,
          });
        } else if (selectedItemData.post_type === "reel") {
          dispatch(setSavedReelList(updatedPostList));
          navigationRef.current.navigate("ViewReelForSave", {
            notificationType: "comment",
            commentPostId: selectedItemData?.postData?.reel_id,
          });
        }
      }
    } else if (selectedItemData?.notificationType === "follow") {
      navigationRef.current.navigate("ProfileNew", {
        data: { user_id: selectedItemData?.user_id },
        type: "anotherProfile",
      });
    } else if (selectedItemData?.notificationType === "reel") {
      if (selectedItemData?.postData && !isEmpty(selectedItemData?.postData)) {
        const updatedReelList = {
          page: 1,
          next_enable: false,
          data: [selectedItemData?.postData],
        };
        dispatch(setSavedReelList(updatedReelList));
        navigationRef.current.navigate("ViewReelForSave");
      }
    }
  };
  useEffect(() => {
    const unsubscribe = messaging().onMessage(async (remoteMessage) => {
      const data = remoteMessage?.data?.meta
        ? JSON.parse(remoteMessage?.data?.meta)
        : remoteMessage?.data
          ? remoteMessage?.data
          : {};

      if (!isEmpty(data) && !isEmpty(data?.notificationType)) {
        dispatch(setNotificationActivity(data?.notificationType));
      }

      if (data?.key !== "chat" && data?.notificationType !== "chat") {
        dispatch(setNotificationCount(Number(data?.notiCount)));
      }

      if (data?.key === "chat") {
        dispatch(setTotalMsgCount(Number(data?.messageCount)));
      }
      if (data?.notificationType === "ratings") {
        dispatch(setFeedBackModal(true));
      }

      if (
        currentStack?.name === "MessagesInfo" &&
        data?.notificationType === "group_chat"
      ) {
        return; // do nothing
      }
      if (!isEmpty(data) && data?.notificationType === "chat") {
        if (data?.messageData?.message_count) {
          dispatch(setTotalMsgCount(Number(data?.messageData?.message_count)));
        }
        if (
          currentStack?.name === "MessagesInfo" &&
          selectedRoom?.conversation_id !== data?.messageData?.conversation_id
        ) {
          if (
            currentStack?.name !== "NotificationScreen" &&
            data?.notificationType !== "ratings"
          ) {
            handleNotification(remoteMessage?.notification, data);
          }
        } else if (
          currentStack?.name !== "chat" &&
          currentStack?.name !== "MessagesInfo"
        ) {
          if (
            currentStack?.name !== "NotificationScreen" &&
            data?.notificationType !== "ratings"
          ) {
            handleNotification(remoteMessage?.notification, data);
          }
        }
      } else {
        if (
          currentStack?.name !== "NotificationScreen" &&
          data?.notificationType !== "ratings"
        ) {
          handleNotification(remoteMessage?.notification, data);
        }
      }
    });

    return unsubscribe;
  }, [currentStack]);

  useEffect(() => {
    const openNotification = messaging().onNotificationOpenedApp(
      async (remoteMessage) => {
        const data = remoteMessage?.data?.meta
          ? JSON.parse(remoteMessage?.data?.meta)
          : remoteMessage?.data
            ? remoteMessage?.data
            : {};
        if (isObject(remoteMessage?.data) && !isEmpty(remoteMessage?.data)) {
          onNotificationClick(data);
        }
      }
    );

    return openNotification;
  }, []);

  useEffect(() => {
    const initNotification = messaging()
      .getInitialNotification()
      .then((remoteMessage) => {
        if (remoteMessage) {
          const data = remoteMessage?.data?.meta
            ? JSON.parse(remoteMessage?.data?.meta)
            : remoteMessage?.data
              ? remoteMessage?.data
              : {};
          onNotificationClick(data);
        }
      });
    return initNotification;
  }, []);

  const onNotificationClick = (data) => {
    if (data?.notificationType === "chat") {
      navigationRef.current.navigate("MessagesInfo", {
        userInfo: data?.messageData,
      });
    } else if (data?.notificationType === "group_request") {
      navigationRef.current.navigate("GroupInfo", {
        groupId: data?.group_details_id,
        from: "notificaiton",
      });
    } else if (data?.notificationType === "group_chat") {
      navigationRef.current.navigate("MessagesInfo", {
        userInfo: data?.messageData,
      });
    } else {
      handleNavigation(data);
    }
  };

  // this function for handle notification
  async function handleNotification(notificationData, data) {
    props.showNotification({
      title: notificationData.title || "Title",
      message: notificationData.body || "Message",
      onPress: () => {
        onNotificationClick(data);
      },
      icon: images.whiteLogo,
    });
    // }
  }
  return <View />;
};

PushNotification.propTypes = {};

PushNotification.defaultProps = {};

export default withInAppNotification(PushNotification);
