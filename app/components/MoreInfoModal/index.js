import React, { useState } from "react";
import {
  Modal,
  Text,
  View,
  TouchableWithoutFeedback,
  KeyboardAvoidingView,
  Keyboard,
  Platform,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
} from "react-native";
import styles from "./styles";
import { translate } from "../../lang/Translate";
import { BaseColors } from "@config/theme";
import { CustomIcon } from "@config/LoadIcons";
import NoRecord from "@components/NoRecord";

const MoreInfoModal = ({
  visible,
  setModalVisible,
  listData = [],
  onBtnPress = () => {},
  loader = false,
}) => {
  const [selectId, setSelectId] = useState("");
  // OverClick Method
  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      setModalVisible(false);
    }
    setModalVisible(false);
    Keyboard.dismiss();
  };
  const renderItem = ({ item, index }) => {
    const data = item;
    return (
      // Item Profile data
      <TouchableOpacity
        activeOpacity={0.8}
        onPress={() => {
          onBtnPress(item);
          setSelectId(item?.id);
        }}
        style={[
          styles.renderItemMainView,
          {
            borderBottomWidth: index === listData?.length - 1 ? null : 0.75,

            borderColor:
              item?.index !== listData?.length
                ? BaseColors.gray
                : BaseColors.white,
            paddingVertical: index === listData?.length - 1 ? null : 20,
            paddingTop: index === listData?.length - 1 ? 20 : null,
          },
        ]}
      >
        <Text
          style={[
            styles.btnTextStyle,
            { color: data?.color ? data?.color : BaseColors.fontColor },
          ]}
        >
          {translate(data?.title)}
        </Text>
        {loader && selectId === item?.id ? (
          <ActivityIndicator animating color={BaseColors.activeTab} />
        ) : (
          <CustomIcon
            name="BsChevronRight"
            size={19}
            color={BaseColors.black}
          />
        )}
      </TouchableOpacity>
    );
  };

  const noRecordView = () => {
    return <NoRecord title="currentlyNotAvailable" />;
  };

  return (
    <Modal
      animationType="slide"
      transparent={true}
      animationInTiming={5000}
      animationOutTiming={5000}
      visible={visible}
      onRequestClose={() => {
        setModalVisible(!visible);
      }}
    >
      <TouchableWithoutFeedback onPress={handleOverlayClick}>
        <KeyboardAvoidingView
          style={styles.centeredView}
          behavior={Platform.OS === "ios" ? "padding" : "height"}
        >
          <View style={styles.ovarlayStyle}>
            <View style={styles.modalView}>
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "space-between",
                }}
              >
                <View />

                <TouchableOpacity
                  style={{
                    borderWidth: 1,
                    height: 24,
                    width: 24,
                    borderRadius: 5,
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                  activeOpacity={0.8}
                  onPress={() => setModalVisible(false)}
                >
                  <CustomIcon name="BsX" size={20} color={BaseColors.black} />
                </TouchableOpacity>
              </View>
              <FlatList
                data={listData}
                renderItem={renderItem}
                ListEmptyComponent={noRecordView}
                bounces={false}
              />
            </View>
          </View>
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export default MoreInfoModal;
