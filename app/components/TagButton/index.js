import React from "react";
import { View, Text, Pressable, StyleSheet, FlatList } from "react-native";

const TagButton = ({ label, onPress, isSpecial = false }) => {
  return (
    <Pressable
      onPress={onPress}
      style={[styles.tag, isSpecial ? styles.specialTag : styles.inactiveTag]}
    >
      <Text
        style={[
          styles.tagText,
          isSpecial ? styles.specialText : styles.inactiveText,
        ]}
      >
        {label}
      </Text>
    </Pressable>
  );
};

export default function TagButtonComponent(props) {
  const { data, navigation } = props;
  const [showAll, setShowAll] = React.useState(false);

  const toggleTag = async (item) => {
    navigation.navigate("Chat", {
      screen: "chatTab",
      params: { search: item?.group_name },
    });
  };

  const handleSeeAll = () => {
    navigation.navigate("SearchScreen", { groupSearch: true });
  };

  // Determine what data to show
  const displayData = React.useMemo(() => {
    if (!data || data.length <= 5) {
      return data; // Show all if 5 or fewer items
    }

    if (showAll) {
      return data; // Show all when expanded
    }

    return data.slice(0, 5); // Show only first 5 items
  }, [data, showAll]);

  // Check if we need to show "See all" button
  const shouldShowSeeAllButton = data && data.length > 5;
  const remainingCount = data ? data.length - 5 : 0;

  return (
    <View style={styles.container}>
      <FlatList
        data={displayData}
        keyExtractor={(item) => item.id}
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{ paddingHorizontal: 20 }}
        renderItem={({ item }) => (
          <TagButton label={item.group_name} onPress={() => toggleTag(item)} />
        )}
        ListFooterComponent={() =>
          shouldShowSeeAllButton ? (
            <TagButton
              label={"Show All"}
              onPress={handleSeeAll}
              isSpecial={true}
            />
          ) : null
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 10,
    paddingVertical: 20,
  },
  tag: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 10,
  },
  activeTag: {
    backgroundColor: "#D6001C", // red fill
  },
  inactiveTag: {
    borderWidth: 1.5,
    borderColor: "#D6001C", // red outline
    backgroundColor: "white",
  },
  tagText: {
    fontSize: 16,
    fontWeight: "bold",
  },
  activeText: {
    color: "white",
  },
  inactiveText: {
    color: "#D6001C",
  },
  specialTag: {
    backgroundColor: "#E8E8E8", // light gray background
    borderWidth: 1.5,
    borderColor: "#999999", // gray outline
  },
  specialText: {
    color: "#666666", // gray text
    fontSize: 14,
  },
  closeButton: {
    marginLeft: 6,
    borderWidth: 1.5,
    borderColor: "white",
    borderRadius: 10,
    width: 18,
    height: 18,
    justifyContent: "center",
    alignItems: "center",
  },
  closeText: {
    color: "white",
    fontSize: 12,
    lineHeight: 12,
    fontWeight: "bold",
  },
});
