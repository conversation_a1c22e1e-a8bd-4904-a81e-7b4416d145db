import React, { useCallback, useEffect, useRef, useState } from "react";
import {
  CameraRoll,
  cameraRollEventEmitter,
} from "@react-native-camera-roll/camera-roll";
import {
  ActivityIndicator,
  AppState,
  Dimensions,
  FlatList,
  Image,
  Platform,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { BaseColors } from "@config/theme";
import isEmpty from "lodash-es/isEmpty";
import Toast from "react-native-simple-toast";
import { useFocusEffect } from "@react-navigation/native";
import { useSelector } from "react-redux";
import Loader from "@components/Loader";
import NoRecord from "@components/NoRecord";

const dayjs = require("dayjs");
const duration = require("dayjs/plugin/duration");
dayjs.extend(duration);

const supportedMimeTypesByTheBackEnd = [
  "image/jpeg",
  "image/png",
  "image/heif",
  "image/heic",
  "image/heif-sequence",
  "image/heic-sequence",
  "video/mp4",
];

WIDTH = Dimensions.get("window").width;

const useGallery = ({
  pageSize = 30,
  mimeTypeFilter = supportedMimeTypesByTheBackEnd,
  assetType = "All",
  maxSelectionLimit = null,
}) => {
  const { adminSettingData } = useSelector((state) => state.auth);
  const imageSizeObject = adminSettingData?.find(
    (obj) => obj?.slug === "IMAGEUPLOADPERPOST"
  );

  // Use custom limit if provided, otherwise use admin setting
  const selectionLimit = maxSelectionLimit || imageSizeObject?.value;

  const isAndroid = Platform.OS === "android";
  const [isLoading, setIsLoading] = useState(false);
  const [isReloading, setIsReloading] = useState(false);
  const [isLoadingNextPage, setIsLoadingNextPage] = useState(false);
  const [hasNextPage, setHasNextPage] = useState(false);
  const [nextCursor, setNextCursor] = useState();
  const [photos, setPhotos] = useState([]);
  const [selectedPhotoIndex, setSelectedPhotoIndex] = useState(null);
  const [defaultSelectedImg, setDefaultSelected] = useState(0);
  const [selectedMultiplePhotoIndex, setSelectedMultiplePhotoIndex] = useState(
    []
  );
  const [selectedPhoto, setSelectedPhoto] = useState(null);
  const [selectedMultiplePhoto, setSelectedMultiplePhoto] = useState(null);

  const convertCameraRollPicturesToImageDtoType = (pictures) => {
    return pictures.map((picture) => {
      const mediaType = picture.node.type;
      const duration =
        mediaType === "video/mp4" || mediaType === "video"
          ? picture.node.image.playableDuration
          : undefined;

      const durationConvert = dayjs.duration(duration, "seconds");
      const minutes = durationConvert.minutes();
      const seconds = durationConvert.seconds();

      const formattedTime = dayjs()
        .startOf("day")
        .add(minutes, "minutes")
        .add(seconds, "seconds")
        .format("mm:ss");
      const finalObj = {
        fileCopyUri: null,
        name: picture?.node?.image?.filename,
        size: picture?.node?.image?.fileSize,
        type:
          Platform.OS === "ios"
            ? `${picture.node.type}/${picture?.node?.image?.extension}`
            : picture?.node?.type,
        // id: picture.node.id,
        uri: picture?.node?.image?.uri,
        // duration: duration,
        // imageObject: picture?.node,
      };

      if (mediaType === "video/mp4" || mediaType === "video") {
        finalObj.duration = formattedTime;
        finalObj.durationInSec = duration;
      }

      return finalObj;
    });
  };

  const selectPhoto = (index) => {
    setSelectedPhotoIndex(index);
  };

  const selectMultiplePhoto = (index) => {
    const selectedIndex = selectedMultiplePhotoIndex?.indexOf(index);
    if (
      selectedMultiplePhotoIndex.length <= selectionLimit ||
      selectedIndex !== -1
    ) {
      if (selectedIndex === -1) {
        setSelectedMultiplePhotoIndex([...selectedMultiplePhotoIndex, index]);
      } else {
        array = selectedMultiplePhotoIndex?.filter((item) => item !== index);
        if (array.length === 0) {
          Toast.show("There must be at least one image");
        } else {
          setSelectedMultiplePhotoIndex(array);
        }
      }
    } else {
      Toast.show(`${selectionLimit} images are allowed`);
    }
  };

  const loadNextPagePictures = useCallback(async () => {
    try {
      nextCursor ? setIsLoadingNextPage(true) : setIsLoading(true);
      const { edges, page_info } = await CameraRoll.getPhotos({
        first: pageSize,
        after: nextCursor,
        assetType: assetType,
        mimeTypes: mimeTypeFilter,
        ...(isAndroid && {
          include: ["fileSize", "filename", "playableDuration"],
        }),
      });
      const photos = convertCameraRollPicturesToImageDtoType(edges);

      setPhotos((prev) => [...prev, ...photos]);
      setNextCursor(page_info.end_cursor);
      setHasNextPage(page_info.has_next_page);
      nextCursor ? setIsLoadingNextPage(false) : setIsLoading(false);
    } catch (error) {
      console.error("useGallery getPhotos error:", error);
      nextCursor ? setIsLoadingNextPage(false) : setIsLoading(false);
    } finally {
      nextCursor ? setIsLoadingNextPage(false) : setIsLoading(false);
    }
  }, [mimeTypeFilter, nextCursor, pageSize]);

  const getUnloadedPictures = useCallback(async () => {
    try {
      setIsReloading(true);
      const { edges, page_info } = await CameraRoll.getPhotos({
        first: !photos || photos.length < pageSize ? pageSize : photos.length,
        assetType: assetType,
        mimeTypes: mimeTypeFilter,
        // Include fileSize only for android since it's causing performance issues on IOS.
        ...(isAndroid && {
          include: ["fileSize", "filename", "playableDuration"],
        }),
      });

      const newPhotos = convertCameraRollPicturesToImageDtoType(edges);
      setPhotos(newPhotos);

      setNextCursor(page_info.end_cursor);
      setHasNextPage(page_info.has_next_page);
    } catch (error) {
      console.error("useGallery getNewPhotos error:", error);
    } finally {
      setIsReloading(false);
    }
  }, [mimeTypeFilter, pageSize, photos]);

  useEffect(() => {
    if (!photos.length) {
      loadNextPagePictures();
    }
  }, [loadNextPagePictures, photos]);

  useEffect(() => {
    const subscription = AppState.addEventListener(
      "change",
      async (nextAppState) => {
        if (nextAppState === "active") {
          getUnloadedPictures();
        }
      }
    );

    return () => {
      subscription.remove();
    };
  }, [getUnloadedPictures]);

  useEffect(() => {
    let subscription;
    if (Platform.OS === "ios") {
      subscription = cameraRollEventEmitter.addListener(
        "onLibrarySelectionChange",
        (_event) => {
          getUnloadedPictures();
        }
      );
    }

    return () => {
      if (subscription) {
        subscription.remove();
      }
    };
  }, [getUnloadedPictures]);

  useEffect(() => {
    if (selectedPhotoIndex !== null && selectedPhotoIndex >= 0) {
      setSelectedPhoto([photos[selectedPhotoIndex]]);
    } else {
      setSelectedPhoto(null);
    }
    setDefaultSelected([photos[0]]);
  }, [selectedPhotoIndex, photos]);

  useEffect(() => {
    if (!isEmpty(selectedMultiplePhotoIndex)) {
      const selectedData = selectedMultiplePhotoIndex
        ?.map((index) => photos[index])
        ?.filter((photo) => photo !== undefined);
      setSelectedMultiplePhoto(selectedData);
    } else {
      setSelectedMultiplePhoto(null);
    }
  }, [selectedMultiplePhotoIndex, photos]);

  return {
    photos,
    selectedPhotoIndex,
    selectedPhoto,
    setSelectedPhoto,
    selectedMultiplePhoto,
    selectPhoto,
    selectMultiplePhoto,
    loadNextPagePictures,
    isLoading,
    isLoadingNextPage,
    isReloading,
    hasNextPage,
    selectedMultiplePhotoIndex,
    setSelectedMultiplePhotoIndex,
    setSelectedPhotoIndex,
    defaultSelectedImg,
  };
};

const CIMagePicker = ({
  multiSelect = false,
  onImageSelect,
  defaultSelected = false,
  assetType = "All",
  goToPost = false,
  setSelectedPostData = () => {},
  maxSelectionLimit = null,
  chatScreen = false,
}) => {
  const {
    photos,
    selectedPhotoIndex,
    selectedPhoto,
    selectedMultiplePhoto,
    selectPhoto,
    setSelectedPhoto,
    selectMultiplePhoto,
    loadNextPagePictures,
    isLoading,
    isLoadingNextPage,
    isReloading,
    hasNextPage,
    selectedMultiplePhotoIndex,
    setSelectedPhotoIndex,
    setSelectedMultiplePhotoIndex,
  } = useGallery({ pageSize: 30, assetType: assetType, maxSelectionLimit });
  console.log("🚀 ~ CIMagePicker ~ maxSelectionLimit:", maxSelectionLimit);

  const scrollViewRef = useRef(null);
  useEffect(() => {
    if (chatScreen) {
      // For chat screen, use debounced callback and only call when there are actual selections
      if (selectedMultiplePhoto && selectedMultiplePhoto.length > 0) {
        console.log(
          "🚀 ~ CIMagePicker ~ selectedMultiplePhoto:",
          selectedMultiplePhoto
        );
        onImageSelect(selectedMultiplePhoto);
      }
    } else {
      // Original logic for non-chat screens
      if (selectedPhoto && selectedMultiplePhoto) {
        onImageSelect(multiSelect ? selectedMultiplePhoto : selectedPhoto);
      }
    }
  }, [selectedPhoto, selectedMultiplePhoto, chatScreen]);

  useEffect(() => {
    setSelectedMultiplePhotoIndex([selectedPhotoIndex]);
  }, [selectedPhotoIndex]);

  useFocusEffect(
    useCallback(() => {
      if (goToPost === true) {
        scrollToTop();
        setSelectedPhoto(null);
        setSelectedMultiplePhotoIndex([]);
        setSelectedPhotoIndex(null);
        setSelectedPostData(false);
      }
    }, [goToPost])
  );

  const scrollToTop = () => {
    if (scrollViewRef.current) {
      scrollViewRef?.current?.scrollToOffset({ offset: 0, animated: false });
    }
  };

  const handleScroll = (event) => {
    const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;

    // Calculate the current scroll position as a percentage of the total content height
    const scrollPercentage =
      (contentOffset.y + layoutMeasurement.height) / contentSize.height - 220;

    // Load more images when the scroll percentage is greater than or equal to 0.5
    if (scrollPercentage >= 0.5) {
      loadNextPagePictures();
    }
  };

  const handleSelected = (DotSelectedIndex) => {
    if (multiSelect) {
      selectMultiplePhoto(DotSelectedIndex);
      setSelectedPostData(true);
    } else {
      selectPhoto(DotSelectedIndex);
      setSelectedPostData(true);
    }
  };
  // Conditionally render empty view if there are no photos
  const renderEmptyView = () => (
    <View style={{ flex: 1, alignItems: "center", justifyContent: "center" }}>
      <NoRecord title="noPictureFound" />
    </View>
  );

  return (
    <View>
      {isLoading && isEmpty(photos) ? (
        <Loader />
      ) : (
        <FlatList
          ref={scrollViewRef}
          data={photos}
          keyExtractor={(item, index) => index.toString()}
          numColumns={3}
          contentContainerStyle={{
            alignItems: "center",
            justifyContent: "center",
          }}
          ListEmptyComponent={renderEmptyView}
          renderItem={({ item, index }) => {
            return (
              <TouchableOpacity
                onPress={() => handleSelected(index)}
                style={{
                  margin: 3,
                  borderColor: "#8E8383",
                  overflow: "hidden",
                }}
                activeOpacity={0.8}
              >
                <View style={{ position: "relative" }}>
                  <Image
                    source={{ uri: item.uri }}
                    style={{ width: WIDTH / 3, height: 166 }}
                  />
                  {item?.duration ? (
                    <View
                      style={{
                        position: "absolute",
                        bottom: 10,
                        right: 10,
                        backgroundColor: BaseColors.activeTab,
                        paddingHorizontal: 5,
                        paddingVertical: 2,
                        borderRadius: 4,
                      }}
                    >
                      <Text style={{ color: BaseColors.white, fontSize: 12 }}>
                        {item?.duration || 0}
                      </Text>
                    </View>
                  ) : null}
                  <View
                    style={{
                      position: "absolute",
                      zIndex: 11111,
                      right: 2,
                      top: 2,
                    }}
                  >
                    {multiSelect ? (
                      selectedMultiplePhotoIndex?.map((id) => {
                        return (
                          <>
                            <RadioButton
                              selected={index === id}
                              onPress={() => selectMultiplePhoto(index)}
                            />
                          </>
                        );
                      })
                    ) : (
                      <RadioButton
                        selected={index === selectedPhotoIndex}
                        onPress={() => selectPhoto(index)}
                      />
                    )}
                  </View>
                </View>
              </TouchableOpacity>
            );
          }}
          onScroll={handleScroll} // Call handleScroll on scroll
          scrollEventThrottle={0.5}
          onEndReached={() => {
            if (hasNextPage && !isLoadingNextPage && !isLoading) {
              loadNextPagePictures();
            }
          }}
          ListFooterComponent={
            isLoadingNextPage ? <ActivityIndicator size={"large"} /> : null
          }
        />
      )}
      {isLoadingNextPage && !isEmpty(photos) ? (
        <ActivityIndicator size={"large"} />
      ) : null}
    </View>
  );
};

const RadioButton = ({ selected, onPress }) => (
  <TouchableOpacity onPress={onPress}>
    <View
      style={{
        width: 20,
        height: 20,
        borderRadius: 10,
        borderWidth: selected ? 2 : 0,
        borderColor: BaseColors.activeTab,
        justifyContent: "center",
        alignItems: "center",
        position: "absolute",
        right: 2,
        top: 2,
      }}
    >
      {selected && (
        <View
          style={{
            width: 10,
            height: 10,
            borderRadius: 5,
            backgroundColor: BaseColors.activeTab,
          }}
        />
      )}
    </View>
  </TouchableOpacity>
);

export default CIMagePicker;
