/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable react-native/no-inline-multiStyles */
import React, {
  useState,
  useImperativeHandle,
  forwardRef,
  useEffect,
  useRef,
  memo,
  useLayoutEffect,
} from "react";
import {
  Dimensions,
  Text,
  TouchableOpacity,
  View,
  Keyboard,
  FlatList,
  Modal,
  TouchableWithoutFeedback,
  I18nManager,
  Platform,
  TextInput,
} from "react-native";
import PropTypes from "prop-types";
import _, { isEmpty } from "lodash";
import Animated, {
  interpolate,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  Easing,
} from "react-native-reanimated";
import { BaseColors } from "@config/theme";
import multiStyles from "./styles";
import { CustomIcon } from "@config/LoadIcons";
import { FontFamily } from "@config/typography";
import { translate } from "../../lang/Translate";
import Toast from "react-native-simple-toast";
import { groupSearchMessage } from "@screens/Chat/apiCallFunction";
import Checkbox from "@components/Checkbox";

const AddTagName = memo(
  forwardRef((props, ref) => {
    const {
      data = [],
      disabled = false,
      onPress = () => {},
      value = [],
      placeholder = "",
      style = {},
      textStyle = {},
      listProps = "title",
      showError = false,
      errorMsg = "",
      label = "",
      labelTextStyle = {},
      multiple = false,
      listPosition = "top",
      onselectedGrop = () => {},
      navigation,
      srollRef,
      setExtraPadding = () => {},
    } = props;

    const rootRef = useRef();
    const IOS = Platform.OS === "ios";
    const valueRef = useRef();
    const listRef = useRef();
    const [showList, setShowList] = useState(false);
    const [optionArr, setOptionArr] = useState([]);
    const [searchQuery, setSearchQuery] = useState("");
    const [position, setPosition] = useState({});
    const [searchGroupList, setSearchGroupList] = useState([]);
    const [selectedGroups, setSelectedGroups] = useState([]);
    const [textInputValue, setTextInputValue] = useState("");

    const [keyboardVisible, setKeyboardVisible] = useState(false);

    useEffect(() => {
      const keyboardDidShowListener = Keyboard.addListener(
        "keyboardDidShow",
        () => {
          setKeyboardVisible(true);
        }
      );
      const keyboardDidHideListener = Keyboard.addListener(
        "keyboardDidHide",
        () => {
          setKeyboardVisible(false);
        }
      );

      return () => {
        keyboardDidShowListener.remove();
        keyboardDidHideListener.remove();
      };
    }, []);

    useEffect(() => {
      onselectedGrop(selectedGroups);
    }, [selectedGroups]);

    useImperativeHandle(ref, () => ({
      listClose() {
        setShowList(false);
      },
      getLabel() {
        const filterValue = multiple
          ? data?.filter((v) => value?.includes(v[label]))
          : data?.find((v) => value == v[label]);
        const title =
          !multiple && _.isObject(filterValue) ? filterValue[listProps] : "";

        return title;
      },
    }));

    useEffect(() => {
      if (_.isArray(data) && !_.isEmpty(data)) {
        if (_.isEmpty(optionArr) || _.isEmpty(searchQuery)) {
          setOptionArr(data);
        }
      }
    }, [data, showList]);

    useLayoutEffect(() => {
      checkViewPosition(false);
    }, [data, showList]);

    const checkViewPosition = (bool = true) => {
      if (rootRef?.current) {
        rootRef.current.measureInWindow((pageX, pageY, width, height) => {
          const H = Dimensions.get("window").height;
          const W = Dimensions.get("window").width;
          const viewHeight = 50;
          const top = height + pageY;
          const bottom = H - height - pageY + viewHeight;
          const left = I18nManager.isRTL ? W - width - pageX : pageX;
          setPosition({
            width: Math.floor(width),
            top: Math.floor(top),
            bottom: Math.floor(bottom),
            left: Math.floor(left),
          });
          bool && setShowList(!showList);
        });
      }
    };

    const onLayout = (event) => {
      const { x, y } = event.nativeEvent.layout;
    };

    const rotation = useSharedValue(0);

    const rotateUp = () => {
      rotation.value = withTiming(180, {
        duration: 200,
        easing: Easing.linear,
      });
    };
    const rotateDown = () => {
      rotation.value = withTiming(0, {
        duration: 200,
        easing: Easing.linear,
      });
    };

    const onRequestClose = () => {
      setShowList(false);
      setTextInputValue("");
      setSearchGroupList([]);
      rotateDown();
    };

    // Function to toggle the accordion by animating the shared value
    const toggleButton = () => {
      if (rotation.value === 0) {
        rotateUp();
        setSearchQuery("");
      } else {
        rotateDown();
      }
    };

    const iconStyle = useAnimatedStyle(() => {
      return {
        transform: [
          {
            rotate: `${interpolate(rotation.value, [0, 180], [0, 180])}deg`,
          },
        ],
      };
    });

    const renderDropDown = () => {
      const removeSelectedItem = (group_id) => {
        setSelectedGroups(
          selectedGroups.filter((g) => g.group_details_id !== group_id)
        );
      };

      return (
        <View ref={rootRef} onLayout={onLayout}>
          {/* Label container here */}
          {label ? (
            <View style={[multiStyles.labelContainer]}>
              <View style={multiStyles.labelAndRequiredIconContainer}>
                <Text style={[labelTextStyle]}>{label}</Text>
              </View>
            </View>
          ) : null}

          <TouchableOpacity
            activeOpacity={0.7}
            disabled={disabled}
            onPress={() => {
              Keyboard?.dismiss();
              setTimeout(checkViewPosition, IOS ? 100 : 0);
              toggleButton();
              onPress();
            }}
            style={multiStyles.mainContainer}
          >
            <FlatList
              ref={valueRef}
              horizontal
              showsHorizontalScrollIndicator={false}
              data={selectedGroups}
              nestedScrollEnabled
              renderItem={({ item, index }) => {
                return (
                  <>
                    <TouchableOpacity
                      activeOpacity={0.8}
                      disabled={disabled}
                      key={`selected_${index}`}
                      onPress={() => removeSelectedItem(item?.group_details_id)}
                      style={{
                        paddingVertical: 10,
                      }}
                    >
                      <View
                        style={{
                          borderWidth: 1,
                          padding: 6,
                          marginRight: 8,
                          marginVertical: 5,
                          borderRadius: 5,
                          backgroundColor: "rgba(241, 242, 244, 1)",
                          borderColor: "rgba(241, 242, 244, 1)",
                        }}
                      >
                        <Text
                          style={{
                            color: BaseColors.black100,
                            fontSize: 16,
                            fontFamily: FontFamily.medium,
                            marginBottom: 3,
                            textTransform: "capitalize",
                          }}
                        >
                          {item?.groupName}
                        </Text>
                        <View
                          style={{
                            position: "absolute",
                            right: -10,
                            top: -10,
                            zIndex: 999,
                          }}
                        >
                          <CustomIcon
                            name="BsX"
                            size={22}
                            color={BaseColors.black}
                          />
                        </View>
                      </View>
                    </TouchableOpacity>
                  </>
                );
              }}
              ListEmptyComponent={() => {
                return (
                  <Text
                    numberOfLines={1}
                    style={[multiStyles.placeholderText, textStyle]}
                  >
                    {translate(placeholder)}
                  </Text>
                );
              }}
            />

            {/* Arrow Button Container */}
            <View style={[multiStyles.iconContainer]}>
              <Animated.View
                style={[multiStyles.arrowIconContainer, iconStyle]}
              >
                <CustomIcon
                  name={"BsChevronDown"}
                  size={20}
                  color={BaseColors.gray2}
                />
              </Animated.View>
            </View>
          </TouchableOpacity>
        </View>
      );
    };

    const handleToSearchText = async (searchBtnData) => {
      // If search text is empty, clear the search results
      if (!searchBtnData?.trim()) {
        setSearchGroupList([]);
        return;
      }

      const resp = await groupSearchMessage(searchBtnData?.trim(), 1);

      if (resp?.data?.success && !isEmpty(resp?.data?.data)) {
        setSearchGroupList({
          data: resp?.data?.data,
        });
      } else {
        // When API returns empty array, show the search text as an option to add
        setSearchGroupList({
          data: [],
          showAddOption: true,
          searchText: searchBtnData?.trim(),
        });
      }
    };

    const toggleGroupSelection = (group) => {
      const exists = selectedGroups.some(
        (g) => g.group_details_id === group.group_details_id
      );
      console.log("🚀 ~ toggleGroupSelection ~ exists:", exists);

      if (exists) {
        // Remove from selection
        setSelectedGroups(
          selectedGroups.filter(
            (g) => g.group_details_id !== group.group_details_id
          )
        );
      } else {
        // Add to selection
        if (selectedGroups.length >= 5) {
          Toast.show("You can only select up to 5 groups");
        } else {
          setSelectedGroups([...selectedGroups, group]);
        }
      }
    };

    const addNewGroupFromText = (groupName) => {
      // Create a new group object from the text input
      const newGroup = {
        group_details_id: `temp_${Date.now()}`, // Temporary ID for new groups
        groupName: groupName,
        groupMemberCount: "New Group",
        isNewGroup: true, // Flag to identify this as a new group
      };

      // Add to selected groups
      setSelectedGroups([...selectedGroups, newGroup]);

      // Clear only the new text input part and search results
      setTextInputValue("");
      setSearchGroupList([]);
    };

    return (
      <View style={[multiStyles.root, style]}>
        {renderDropDown()}
        {showError ? (
          <View style={multiStyles.errorMsgMainView}>
            <CustomIcon
              name={"BsExclamationCircle"}
              size={18}
              color={"#D6002E"}
            />
            <Text style={multiStyles.errorTxt}>{translate(errorMsg)}</Text>
          </View>
        ) : null}
        <Modal transparent visible={showList} onRequestClose={onRequestClose}>
          <TouchableWithoutFeedback onPress={onRequestClose}>
            <View style={[{ flex: 1 }]}>
              <View
                style={[
                  multiStyles.mainListContainer,
                  { left: position?.left, top: position?.top },
                ]}
              >
                <TouchableOpacity
                  activeOpacity={1}
                  style={[
                    multiStyles.listContainer,
                    {
                      width: position?.width,
                      height:
                        optionArr?.length > 5
                          ? Dimensions.get("screen").height / 3
                          : null,
                    },
                  ]}
                >
                  <View
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                    }}
                  >
                    <TextInput
                      placeholder="Search..."
                      value={textInputValue}
                      onChangeText={(text) => {
                        let cleanText = text;
                        // Allow empty input
                        if (cleanText === "") {
                          setTextInputValue("");
                          return;
                        }

                        // 1. Ensure only one # and only at the start
                        if (!cleanText.startsWith("#")) {
                          cleanText = "#" + cleanText.replace(/#/g, ""); // force # at beginning
                        } else {
                          cleanText =
                            "#" + cleanText.slice(1).replace(/#/g, ""); // remove extra #
                        }

                        // 2. Remove any special characters except #
                        cleanText = cleanText.replace(/[^a-zA-Z0-9#]/g, "");

                        // 3. Limit to 15 characters
                        cleanText = cleanText.slice(0, 20);

                        setTextInputValue(cleanText);
                        handleToSearchText(cleanText);
                      }}
                      style={{
                        borderColor: "#555454",
                        borderWidth: 0.8,
                        borderRadius: 5,
                        margin: 10,
                        height: 40,
                        width: "90%",
                        paddingLeft: 8,
                        color: BaseColors.black,
                      }}
                      onFocus={() => {
                        setTimeout(() => {
                          srollRef?.current?.scrollTo({
                            y: Dimensions.get("window").height / 2,
                            animated: true,
                          });
                        }, 300); // wait for keyboard to open
                      }}
                    />
                  </View>
                  {searchGroupList?.data?.length > 0 && (
                    <View style={multiStyles.dropdown}>
                      <FlatList
                        data={
                          searchGroupList?.data?.length > 0
                            ? searchGroupList?.data
                            : searchGroupList?.showAddOption
                              ? [
                                  {
                                    group_details_id: "add_new",
                                    groupName: searchGroupList?.searchText,
                                    groupMemberCount: "Add new group",
                                    isAddOption: true,
                                  },
                                ]
                              : []
                        }
                        keyExtractor={(item) =>
                          item.group_details_id || item.tag
                        }
                        renderItem={({ item }) => {
                          const isChecked =
                            selectedGroups &&
                            selectedGroups.some(
                              (g) =>
                                g.group_details_id === item?.group_details_id
                            );
                          return (
                            <TouchableOpacity
                              style={multiStyles.dropdownItem}
                              activeOpacity={0.6}
                              onPress={() => {
                                // Only make row clickable when there's no button showing
                                if (item.isAddOption) {
                                  addNewGroupFromText(item.groupName);
                                } else {
                                  toggleGroupSelection(item);
                                }
                              }}
                            >
                              <View
                                style={{
                                  flexDirection: "row",
                                  alignItems: "center",
                                }}
                              >
                                <View style={multiStyles.hashIcon}>
                                  <Text style={multiStyles.hashText}>#</Text>
                                </View>
                                <View>
                                  <Text style={multiStyles.tagText}>
                                    {item.groupName}
                                  </Text>
                                  <Text style={multiStyles.subText}>
                                    {item.groupMemberCount}
                                  </Text>
                                </View>
                              </View>
                              <Checkbox
                                isChecked={isChecked}
                                toggleCheckbox={() => {
                                  toggleGroupSelection(item);
                                }}
                              />
                            </TouchableOpacity>
                          );
                        }}
                      />
                    </View>
                  )}
                  {isEmpty(searchGroupList?.data) && textInputValue !== "" && (
                    <View
                      style={[
                        multiStyles.dropdownItem,
                        { borderRadius: 5, borderWidth: 1 },
                      ]}
                    >
                      <View
                        style={{
                          flexDirection: "row",
                          alignItems: "center",
                        }}
                      >
                        <View>
                          <Text style={multiStyles.tagText}>
                            {textInputValue || ""}
                          </Text>
                        </View>
                      </View>
                      <TouchableOpacity
                        style={{
                          paddingVertical: 5,
                          paddingHorizontal: 10,
                          borderWidth: 1,
                          borderRadius: 3,
                          borderColor: BaseColors.activeTab,
                        }}
                        activeOpacity={0.8}
                        onPress={() => {
                          navigation.navigate("CreateGroupChat", {
                            groupName: textInputValue,
                            from: "createPostScreen",
                          });
                          setTimeout(() => {
                            onRequestClose();
                            setTextInputValue("");
                            setSearchGroupList([]);
                          }, 300);
                        }}
                      >
                        <Text
                          style={{
                            fontSize: 14,
                            fontFamily: FontFamily.RobotoMedium,
                            color: BaseColors.activeTab,
                          }}
                        >
                          {"Add"}
                        </Text>
                      </TouchableOpacity>
                    </View>
                  )}
                </TouchableOpacity>
              </View>
            </View>
          </TouchableWithoutFeedback>
        </Modal>
      </View>
    );
  })
);

AddTagName.propTypes = {
  data: PropTypes.array,
  disabled: PropTypes.bool,
  onPress: PropTypes.func,
  value: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
    PropTypes.array,
  ]),
  placeholder: PropTypes.string,
  style: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
  textStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
  label: PropTypes.string,
  onChange: PropTypes.func,
  listProps: PropTypes?.string,
  errorMsg: PropTypes.string,
  ListEmptyComponent: PropTypes.func,
  showError: PropTypes.bool,
  listEmptyText: PropTypes.string,
  isSearch: PropTypes.bool,
  valueProp: PropTypes.string,
  labelTextStyle: PropTypes.object,
  required: PropTypes.bool,
  multiple: PropTypes.bool,
  listPosition: PropTypes.string,
  onEndReached: PropTypes.func,
  selectedGroups: PropTypes.array,
};

export default AddTagName;
