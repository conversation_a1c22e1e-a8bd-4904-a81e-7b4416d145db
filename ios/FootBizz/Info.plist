<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>FootBizz</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>1.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
	<string>footbizz</string>
	</array>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>fb1539990646556467</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.380780628082-ts50l14a4jg5l5mhrnvk5kulpk3fefu8</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>CodePushDeploymentKey</key>
	<string>mabx3r8GXCkWUO2RmX1KuxH38rnf9wfqJJMyT</string>
	<key>FacebookAppID</key>
	<string>1539990646556467</string>
	<key>FacebookClientToken</key>
	<string>********************************</string>
	<key>FacebookDisplayName</key>
	<string>FootBizz</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
	<key>NSAllowsArbitraryLoads</key>
	<true/>
	</dict>
	<key>NSAppleMusicUsageDescription</key>
	<string>FootBizz to access video and photos.</string>
	<key>NSCameraUsageDescription</key>
	<string>FootBizz needs access to your Camera for create a Reels, Post and Profile picture</string>
	<key>NSLocationAccuracyUsageDescription</key>
	<string>FootBizz needs access to your LocationAccuracy.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>We need access to your location to provide location-based services and improve your experience.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>FootBizz need to access your location for better user exprience</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>FootBizz needs access to your Microphone for Voice message in one to one in-app chat.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>FootBizz need to access your photos library for change profile picture and upload post and reels.</string>
	<key>UIAppFonts</key>
	<array>
		<string>fonts/AntDesign.ttf</string>
		<string>fonts/Entypo.ttf</string>
		<string>fonts/EvilIcons.ttf</string>
		<string>fonts/Feather.ttf</string>
		<string>fonts/FontAwesome.ttf</string>
		<string>fonts/FontAwesome5_Brands.ttf</string>
		<string>fonts/FontAwesome5_Regular.ttf</string>
		<string>fonts/FontAwesome5_Solid.ttf</string>
		<string>fonts/FontAwesome6_Brands.ttf</string>
		<string>fonts/FontAwesome6_Regular.ttf</string>
		<string>fonts/FontAwesome6_Solid.ttf</string>
		<string>fonts/Fontisto.ttf</string>
		<string>fonts/Foundation.ttf</string>
		<string>fonts/icomoon.ttf</string>
		<string>fonts/Inter-Bold.ttf</string>
		<string>fonts/Inter-Medium.ttf</string>
		<string>fonts/Inter-Regular.ttf</string>
		<string>fonts/Inter-SemiBold.ttf</string>
		<string>fonts/Ionicons.ttf</string>
		<string>fonts/MaterialCommunityIcons.ttf</string>
		<string>fonts/MaterialIcons.ttf</string>
		<string>fonts/Octicons.ttf</string>
		<string>fonts/SimpleLineIcons.ttf</string>
		<string>fonts/Zocial.ttf</string>
		<string>fonts/Roboto-Regular.ttf </string>
		<string>fonts/Roboto-Medium.ttf</string>
		<string>fonts/Roboto-Italic.ttf</string>
		<string>fonts/Roboto-Bold.ttf</string>
		<string>fonts/Roboto-Black.ttf</string>
		<string>fonts/Outfit-Regular.ttf</string>
		<string>fonts/Outfit-Black.ttf</string>
		<string>fonts/Outfit-SemiBold.ttf</string>
		<string>fonts/Outfit-Medium.ttf</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UIStatusBarStyle</key>
	<string></string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>bugsnag</key>
	<dict>
		<key>apiKey</key>
		<string>d4005f25e15dabbce1276a748f018298</string>
	</dict>
</dict>
</plist>
